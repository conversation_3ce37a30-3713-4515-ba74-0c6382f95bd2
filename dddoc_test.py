#!/usr/bin/env python3
"""
使用dddoc工具测试验证码识别系统
"""

import os
import sys
import time
from neural_captcha_model import NeuralCaptchaModel


def test_with_dddoc():
    """使用dddoc工具测试验证码识别"""
    print("🔧 使用dddoc工具测试验证码识别系统")
    print("=" * 60)
    
    # 初始化神经网络模型
    print("📦 初始化神经网络模型...")
    neural_model = NeuralCaptchaModel()
    
    # 模型路径
    model_path = 'models/neural_captcha_model.pkl'
    
    # 检查模型文件
    if not os.path.exists(model_path):
        print(f"❌ 模型文件不存在: {model_path}")
        print("请先运行 python neural_captcha_model.py 训练模型")
        return False
    
    # 加载模型
    print("🔄 加载训练好的模型...")
    if not neural_model.load_model(model_path):
        print(f"❌ 无法加载模型: {model_path}")
        return False
    
    print("✅ 模型加载成功！")
    
    # 测试图片路径
    test_images = [
        'guess_images/image.png',
        'guess_images/3qh4.png', 
        'guess_images/wwd2.png',
        'guess_images/y4h7.png',
        'guess_images/z4pp.png'
    ]
    
    print(f"\n🧪 开始测试 {len(test_images)} 张图片...")
    print("-" * 60)
    
    success_count = 0
    total_count = len(test_images)
    
    for i, image_path in enumerate(test_images, 1):
        print(f"\n[{i}/{total_count}] 测试图片: {os.path.basename(image_path)}")
        
        if not os.path.exists(image_path):
            print(f"❌ 图片文件不存在: {image_path}")
            continue
        
        # 从文件名提取真实标签
        filename = os.path.basename(image_path)
        if filename == 'image.png':
            true_label = 'image'
        else:
            true_label = os.path.splitext(filename)[0].lower()
        
        print(f"   真实标签: {true_label}")
        
        # 开始计时
        start_time = time.time()
        
        # 识别验证码
        try:
            result = neural_model.predict_captcha(image_path)
            
            if result:
                predicted_label = result['result'].lower()
                confidence = result['confidence']
                processing_time = time.time() - start_time
                
                print(f"   识别结果: {predicted_label}")
                print(f"   置信度: {confidence:.3f}")
                print(f"   处理时间: {processing_time:.3f}秒")
                
                # 检查是否正确
                if predicted_label == true_label:
                    print("   ✅ 识别正确！")
                    success_count += 1
                else:
                    print("   ❌ 识别错误")
                    # 详细分析错误
                    print("   错误分析:")
                    for j, (true_char, pred_char) in enumerate(zip(true_label, predicted_label)):
                        if true_char != pred_char:
                            print(f"     位置{j+1}: '{true_char}' -> '{pred_char}' ❌")
                        else:
                            print(f"     位置{j+1}: '{true_char}' ✅")
            else:
                print("   ❌ 识别失败 - 无法处理图片")
                
        except Exception as e:
            print(f"   ❌ 识别过程中出错: {e}")
    
    # 输出总结
    print("\n" + "=" * 60)
    print("📊 测试结果总结")
    print("=" * 60)
    
    accuracy = (success_count / total_count) * 100 if total_count > 0 else 0
    print(f"🎯 测试图片数量: {total_count}")
    print(f"✅ 识别正确数量: {success_count}")
    print(f"❌ 识别错误数量: {total_count - success_count}")
    print(f"📈 准确率: {accuracy:.2f}%")
    
    if accuracy >= 80:
        print("🎉 优秀！识别准确率很高")
    elif accuracy >= 60:
        print("👍 良好！识别准确率不错")
    elif accuracy >= 40:
        print("⚠️  一般，还有改进空间")
    else:
        print("🔧 需要进一步优化模型")
    
    return accuracy >= 40


def test_model_info():
    """测试模型信息"""
    print("\n🔍 模型信息检查")
    print("-" * 40)
    
    model_path = 'models/neural_captcha_model.pkl'
    
    if os.path.exists(model_path):
        file_size = os.path.getsize(model_path) / (1024 * 1024)  # MB
        print(f"📁 模型文件: {model_path}")
        print(f"📏 文件大小: {file_size:.2f} MB")
        
        # 尝试加载模型获取更多信息
        try:
            neural_model = NeuralCaptchaModel()
            if neural_model.load_model(model_path):
                print(f"🔤 字符集: {neural_model.charset}")
                print(f"📐 图像尺寸: {neural_model.img_height}x{neural_model.img_width}")
                print("✅ 模型状态: 正常")
            else:
                print("❌ 模型状态: 加载失败")
        except Exception as e:
            print(f"❌ 模型检查出错: {e}")
    else:
        print(f"❌ 模型文件不存在: {model_path}")


def main():
    """主函数"""
    print("🚀 dddoc工具 - 验证码识别系统测试")
    print("=" * 60)
    
    # 检查Python版本
    python_version = sys.version_info
    print(f"🐍 Python版本: {python_version.major}.{python_version.minor}.{python_version.micro}")
    
    # 检查当前工作目录
    current_dir = os.getcwd()
    print(f"📂 当前目录: {current_dir}")
    
    # 测试模型信息
    test_model_info()
    
    # 执行主要测试
    success = test_with_dddoc()
    
    print(f"\n🏁 测试完成！结果: {'成功' if success else '需要改进'}")
    
    return success


if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n\n⚠️  用户中断测试")
    except Exception as e:
        print(f"\n❌ 测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
