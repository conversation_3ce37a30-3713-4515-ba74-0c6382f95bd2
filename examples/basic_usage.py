#!/usr/bin/env python3
"""
验证码识别工具基本使用示例
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from captcha_recognizer import <PERSON><PERSON>Recognizer
from utils import CaptchaGenerator


def example_1_train_and_recognize():
    """示例1: 训练模型并识别验证码"""
    print("=== 示例1: 训练模型并识别验证码 ===")
    
    # 创建识别器
    recognizer = CaptchaRecognizer('models/example_model.pkl')
    
    # 训练模型（使用生成的数据）
    print("1. 训练模型...")
    success = recognizer.train_model(
        model_type='svm',
        num_samples_per_char=50  # 为了快速演示，使用较少的样本
    )
    
    if not success:
        print("模型训练失败")
        return
    
    # 生成一些测试验证码
    print("2. 生成测试验证码...")
    generator = CaptchaGenerator()
    
    test_cases = ['1234', 'ABCD', '5678', 'WXYZ']
    for text in test_cases:
        image_path = f'test_images/{text}_example.png'
        os.makedirs('test_images', exist_ok=True)
        generator.generate_simple_captcha(text, image_path)
        
        # 识别验证码
        result = recognizer.recognize(image_path, show_process=True)
        
        if result:
            print(f"图像: {image_path}")
            print(f"真实值: {text}")
            print(f"识别结果: {result['result']}")
            print(f"置信度: {result['confidence']:.3f}")
            print(f"正确性: {'✓' if result['result'] == text else '✗'}")
        else:
            print(f"识别失败: {image_path}")
        
        print("-" * 50)


def example_2_load_model_and_recognize():
    """示例2: 加载已训练的模型进行识别"""
    print("\n=== 示例2: 加载已训练的模型进行识别 ===")
    
    # 检查是否有已训练的模型
    model_path = 'models/example_model.pkl'
    if not os.path.exists(model_path):
        print("没有找到预训练模型，请先运行示例1")
        return
    
    # 创建识别器并加载模型
    recognizer = CaptchaRecognizer(model_path)
    
    if not recognizer.is_model_loaded:
        print("模型加载失败")
        return
    
    # 显示模型信息
    model_info = recognizer.get_model_info()
    print(f"模型信息: {model_info}")
    
    # 生成新的测试验证码
    generator = CaptchaGenerator()
    test_text = 'TEST'
    image_path = 'test_images/test_example.png'
    
    generator.generate_simple_captcha(test_text, image_path)
    
    # 识别
    result = recognizer.recognize(image_path)
    
    if result:
        print(f"识别结果: {result['result']}")
        print(f"真实值: {test_text}")
        print(f"置信度: {result['confidence']:.3f}")
        print(f"字符数量: {result['character_count']}")
    else:
        print("识别失败")


def example_3_batch_recognition():
    """示例3: 批量识别验证码"""
    print("\n=== 示例3: 批量识别验证码 ===")
    
    # 检查模型
    model_path = 'models/example_model.pkl'
    if not os.path.exists(model_path):
        print("没有找到预训练模型，请先运行示例1")
        return
    
    recognizer = CaptchaRecognizer(model_path)
    
    # 生成批量测试数据
    generator = CaptchaGenerator()
    batch_dir = 'test_images/batch'
    os.makedirs(batch_dir, exist_ok=True)
    
    test_data = generator.generate_batch_captchas(10, batch_dir)
    
    # 批量识别
    image_paths = [data[0] for data in test_data]
    results = recognizer.recognize_batch(image_paths)
    
    # 统计结果
    correct = 0
    total = len(results)
    
    print("批量识别结果:")
    for i, result_data in enumerate(results):
        image_path = result_data['image_path']
        result = result_data['recognition_result']
        
        # 从文件名获取真实标签
        filename = os.path.basename(image_path)
        true_label = filename.split('_')[0]
        
        if result:
            predicted = result['result']
            confidence = result['confidence']
            is_correct = predicted == true_label
            
            if is_correct:
                correct += 1
            
            print(f"{i+1:2d}. {filename:15s} | 真实: {true_label} | 预测: {predicted} | "
                  f"置信度: {confidence:.3f} | {'✓' if is_correct else '✗'}")
        else:
            print(f"{i+1:2d}. {filename:15s} | 识别失败")
    
    accuracy = correct / total if total > 0 else 0
    print(f"\n批量识别准确率: {accuracy:.3f} ({correct}/{total})")


def example_4_compare_models():
    """示例4: 比较不同模型的性能"""
    print("\n=== 示例4: 比较不同模型的性能 ===")
    
    import time
    
    models = ['svm', 'random_forest']
    results = {}
    
    # 生成测试数据
    generator = CaptchaGenerator()
    test_cases = ['1234', 'ABCD', '5678', 'WXYZ', '9999']
    
    for model_type in models:
        print(f"\n测试 {model_type.upper()} 模型...")
        
        model_path = f'models/{model_type}_comparison.pkl'
        recognizer = CaptchaRecognizer(model_path)
        
        # 训练模型
        start_time = time.time()
        success = recognizer.train_model(
            model_type=model_type,
            num_samples_per_char=30  # 较少样本用于快速比较
        )
        training_time = time.time() - start_time
        
        if not success:
            print(f"{model_type} 模型训练失败")
            continue
        
        # 测试识别性能
        correct = 0
        total = 0
        recognition_times = []
        
        for text in test_cases:
            image_path = f'test_images/{text}_comparison.png'
            generator.generate_simple_captcha(text, image_path)
            
            start_time = time.time()
            result = recognizer.recognize(image_path)
            recognition_time = time.time() - start_time
            
            recognition_times.append(recognition_time)
            
            if result and result['result'] == text:
                correct += 1
            total += 1
        
        accuracy = correct / total
        avg_recognition_time = sum(recognition_times) / len(recognition_times)
        
        results[model_type] = {
            'accuracy': accuracy,
            'training_time': training_time,
            'avg_recognition_time': avg_recognition_time
        }
        
        print(f"  准确率: {accuracy:.3f}")
        print(f"  训练时间: {training_time:.2f}秒")
        print(f"  平均识别时间: {avg_recognition_time:.4f}秒")
    
    # 显示比较结果
    print("\n=== 模型性能比较 ===")
    print(f"{'模型':<15} {'准确率':<10} {'训练时间(秒)':<12} {'识别时间(秒)':<12}")
    print("-" * 50)
    
    for model_type, metrics in results.items():
        print(f"{model_type.upper():<15} {metrics['accuracy']:<10.3f} "
              f"{metrics['training_time']:<12.2f} {metrics['avg_recognition_time']:<12.4f}")


def main():
    """主函数"""
    print("验证码识别工具使用示例")
    print("=" * 50)
    
    # 创建必要的目录
    os.makedirs('models', exist_ok=True)
    os.makedirs('test_images', exist_ok=True)
    
    try:
        # 运行示例
        example_1_train_and_recognize()
        example_2_load_model_and_recognize()
        example_3_batch_recognition()
        example_4_compare_models()
        
        print("\n所有示例运行完成！")
        
    except KeyboardInterrupt:
        print("\n用户中断执行")
    except Exception as e:
        print(f"\n运行出错: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
