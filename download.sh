#!/bin/bash

# 用法: ./download.sh 10   表示下载10次

# 参数检查
if [ $# -ne 1 ]; then
    echo "用法: $0 <下载次数>"
    exit 1
fi

N=$1
URL="https://dbba.sacinfo.org.cn/portal/validate-code?pk=8957bc94e880e4ce5d95fcc6020cf42c6d98d799798d2fd0b4edd58ab79d4f22&t=1757041263235"
OUT_DIR="./test_image"

# 创建目录
mkdir -p "$OUT_DIR"

for ((i=1; i<=N; i++)); do
    # 随机等待 5-10 秒
    SLEEP_TIME=$((RANDOM % 6 + 5))
    echo "第 $i 次下载，等待 $SLEEP_TIME 秒..."
    sleep $SLEEP_TIME

    # 下载并保存
    FILE="$OUT_DIR/image_$i.png"
    curl -s -o "$FILE" "$URL"
    if [ $? -eq 0 ]; then
        echo "已保存: $FILE"
    else
        echo "下载失败: $FILE"
    fi
done

echo "全部下载完成，共 $N 次"
