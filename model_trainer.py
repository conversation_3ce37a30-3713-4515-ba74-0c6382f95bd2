import numpy as np
import pickle
import os
from sklearn.svm import SVC
from sklearn.ensemble import RandomForestClassifier
from sklearn.model_selection import train_test_split, cross_val_score, GridSearchCV
from sklearn.metrics import classification_report, confusion_matrix, accuracy_score
from sklearn.preprocessing import StandardScaler
import cv2
import string
from image_processor import ImageProcessor
from feature_extractor import FeatureExtractor


class ModelTrainer:
    """模型训练类，负责训练字符分类器"""
    
    def __init__(self):
        self.image_processor = ImageProcessor()
        self.feature_extractor = FeatureExtractor()
        self.scaler = StandardScaler()
        self.model = None
        self.model_type = None
        
        # 支持的字符集（数字和字母）
        self.charset = string.digits + string.ascii_uppercase
        
    def generate_training_data(self, num_samples_per_char=100, image_size=(28, 28)):
        """生成训练数据（使用字体渲染）"""
        print("生成训练数据...")
        
        X = []
        y = []
        
        fonts = [
            cv2.FONT_HERSHEY_SIMPLEX,
            cv2.FONT_HERSHEY_PLAIN,
            cv2.FONT_HERSHEY_DUPLEX,
            cv2.FONT_HERSHEY_COMPLEX,
            cv2.FONT_HERSHEY_TRIPLEX,
            cv2.FONT_HERSHEY_COMPLEX_SMALL,
            cv2.FONT_HERSHEY_SCRIPT_SIMPLEX,
            cv2.FONT_HERSHEY_SCRIPT_COMPLEX
        ]
        
        for char in self.charset:
            print(f"生成字符 '{char}' 的样本...")
            
            for i in range(num_samples_per_char):
                # 创建空白图像
                img = np.ones(image_size, dtype=np.uint8) * 255
                
                # 随机选择字体
                font = np.random.choice(fonts)
                
                # 随机字体大小
                font_scale = np.random.uniform(0.5, 1.2)
                
                # 随机粗细
                thickness = np.random.randint(1, 3)
                
                # 计算文本大小以居中
                text_size = cv2.getTextSize(char, font, font_scale, thickness)[0]
                text_x = (image_size[1] - text_size[0]) // 2
                text_y = (image_size[0] + text_size[1]) // 2
                
                # 添加一些随机偏移
                text_x += np.random.randint(-3, 4)
                text_y += np.random.randint(-3, 4)
                
                # 绘制字符
                cv2.putText(img, char, (text_x, text_y), font, font_scale, (0, 0, 0), thickness)
                
                # 添加一些噪声
                if np.random.random() < 0.3:
                    noise = np.random.randint(0, 50, image_size, dtype=np.uint8)
                    img = cv2.add(img, noise)
                
                # 随机旋转
                if np.random.random() < 0.3:
                    angle = np.random.uniform(-15, 15)
                    center = (image_size[1]//2, image_size[0]//2)
                    rotation_matrix = cv2.getRotationMatrix2D(center, angle, 1.0)
                    img = cv2.warpAffine(img, rotation_matrix, image_size, 
                                       borderMode=cv2.BORDER_CONSTANT, borderValue=255)
                
                # 提取特征
                features = self.feature_extractor.extract_combined_features(
                    img, feature_types=['hog', 'contour']
                )
                
                X.append(features)
                y.append(char)
        
        return np.array(X), np.array(y)
    
    def load_training_data_from_images(self, data_dir):
        """从图像文件加载训练数据"""
        X = []
        y = []
        
        if not os.path.exists(data_dir):
            print(f"数据目录 {data_dir} 不存在")
            return np.array([]), np.array([])
        
        for char in self.charset:
            char_dir = os.path.join(data_dir, char)
            if os.path.exists(char_dir):
                for filename in os.listdir(char_dir):
                    if filename.lower().endswith(('.png', '.jpg', '.jpeg')):
                        image_path = os.path.join(char_dir, filename)
                        
                        # 加载和预处理图像
                        image = self.image_processor.load_image(image_path)
                        if image is not None:
                            gray = self.image_processor.to_grayscale(image)
                            resized = self.image_processor.resize_character(gray)
                            
                            # 提取特征
                            features = self.feature_extractor.extract_combined_features(
                                resized, feature_types=['hog', 'contour']
                            )
                            
                            X.append(features)
                            y.append(char)
        
        return np.array(X), np.array(y)
    
    def train_svm_model(self, X, y, test_size=0.2):
        """训练SVM模型"""
        print("训练SVM模型...")

        # 检查每个类别的样本数量
        unique_classes, class_counts = np.unique(y, return_counts=True)
        min_samples = np.min(class_counts)

        # 如果有类别样本数少于2，不使用分层抽样
        if min_samples < 2:
            print(f"警告: 最少样本数为 {min_samples}，不使用分层抽样")
            X_train, X_test, y_train, y_test = train_test_split(
                X, y, test_size=test_size, random_state=42, stratify=None
            )
        else:
            # 分割数据
            X_train, X_test, y_train, y_test = train_test_split(
                X, y, test_size=test_size, random_state=42, stratify=y
            )
        
        # 特征标准化
        X_train_scaled = self.scaler.fit_transform(X_train)
        X_test_scaled = self.scaler.transform(X_test)
        
        # 网格搜索最佳参数
        param_grid = {
            'C': [0.1, 1, 10, 100],
            'gamma': ['scale', 'auto', 0.001, 0.01, 0.1, 1],
            'kernel': ['rbf', 'linear']
        }
        
        svm = SVC(random_state=42)
        grid_search = GridSearchCV(svm, param_grid, cv=3, scoring='accuracy', n_jobs=-1)
        grid_search.fit(X_train_scaled, y_train)
        
        # 使用最佳参数训练模型
        self.model = grid_search.best_estimator_
        self.model_type = 'svm'
        
        # 评估模型
        y_pred = self.model.predict(X_test_scaled)
        accuracy = accuracy_score(y_test, y_pred)
        
        print(f"最佳参数: {grid_search.best_params_}")
        print(f"测试集准确率: {accuracy:.4f}")
        print("\n分类报告:")
        print(classification_report(y_test, y_pred))
        
        return accuracy
    
    def train_random_forest_model(self, X, y, test_size=0.2):
        """训练随机森林模型"""
        print("训练随机森林模型...")
        
        # 分割数据
        X_train, X_test, y_train, y_test = train_test_split(
            X, y, test_size=test_size, random_state=42, stratify=y
        )
        
        # 特征标准化（随机森林不一定需要，但可以提高性能）
        X_train_scaled = self.scaler.fit_transform(X_train)
        X_test_scaled = self.scaler.transform(X_test)
        
        # 网格搜索最佳参数
        param_grid = {
            'n_estimators': [50, 100, 200],
            'max_depth': [None, 10, 20, 30],
            'min_samples_split': [2, 5, 10],
            'min_samples_leaf': [1, 2, 4]
        }
        
        rf = RandomForestClassifier(random_state=42)
        grid_search = GridSearchCV(rf, param_grid, cv=3, scoring='accuracy', n_jobs=-1)
        grid_search.fit(X_train_scaled, y_train)
        
        # 使用最佳参数训练模型
        self.model = grid_search.best_estimator_
        self.model_type = 'random_forest'
        
        # 评估模型
        y_pred = self.model.predict(X_test_scaled)
        accuracy = accuracy_score(y_test, y_pred)
        
        print(f"最佳参数: {grid_search.best_params_}")
        print(f"测试集准确率: {accuracy:.4f}")
        print("\n分类报告:")
        print(classification_report(y_test, y_pred))
        
        return accuracy
    
    def save_model(self, model_path='models/captcha_model.pkl'):
        """保存模型"""
        if self.model is None:
            print("没有训练好的模型可以保存")
            return
        
        os.makedirs(os.path.dirname(model_path), exist_ok=True)
        
        model_data = {
            'model': self.model,
            'scaler': self.scaler,
            'model_type': self.model_type,
            'charset': self.charset
        }
        
        with open(model_path, 'wb') as f:
            pickle.dump(model_data, f)
        
        print(f"模型已保存到: {model_path}")
    
    def load_model(self, model_path='models/captcha_model.pkl'):
        """加载模型"""
        if not os.path.exists(model_path):
            print(f"模型文件 {model_path} 不存在")
            return False
        
        with open(model_path, 'rb') as f:
            model_data = pickle.load(f)
        
        self.model = model_data['model']
        self.scaler = model_data['scaler']
        self.model_type = model_data['model_type']
        self.charset = model_data['charset']
        
        print(f"模型已从 {model_path} 加载")
        return True
    
    def predict_character(self, char_image):
        """预测单个字符"""
        if self.model is None:
            print("模型未训练或加载")
            return None
        
        # 提取特征
        features = self.feature_extractor.extract_combined_features(
            char_image, feature_types=['hog', 'contour']
        )
        
        # 标准化特征
        features_scaled = self.scaler.transform([features])
        
        # 预测
        prediction = self.model.predict(features_scaled)[0]
        
        # 获取预测概率（如果模型支持）
        if hasattr(self.model, 'predict_proba'):
            probabilities = self.model.predict_proba(features_scaled)[0]
            confidence = np.max(probabilities)
        else:
            confidence = 1.0
        
        return prediction, confidence


if __name__ == "__main__":
    # 测试代码
    trainer = ModelTrainer()
    
    # 生成训练数据
    print("生成训练数据...")
    X, y = trainer.generate_training_data(num_samples_per_char=50)
    
    if len(X) > 0:
        print(f"生成了 {len(X)} 个训练样本")
        print(f"特征维度: {X.shape[1]}")
        print(f"字符类别数: {len(np.unique(y))}")
        
        # 训练SVM模型
        svm_accuracy = trainer.train_svm_model(X, y)
        
        # 保存模型
        trainer.save_model('models/svm_model.pkl')
        
        # 训练随机森林模型
        rf_accuracy = trainer.train_random_forest_model(X, y)
        
        # 保存模型
        trainer.save_model('models/rf_model.pkl')
        
        print(f"\nSVM准确率: {svm_accuracy:.4f}")
        print(f"随机森林准确率: {rf_accuracy:.4f}")
    else:
        print("未能生成训练数据")
