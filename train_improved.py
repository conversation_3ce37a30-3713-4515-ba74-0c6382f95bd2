#!/usr/bin/env python3
"""
使用改进的图像处理器训练模型
"""

import os
import numpy as np
from improved_image_processor import ImprovedImageProcessor
from feature_extractor import FeatureExtractor
from model_trainer import ModelTrainer
import string


class ImprovedCaptchaTrainer:
    """使用改进图像处理器的验证码训练器"""
    
    def __init__(self):
        self.image_processor = ImprovedImageProcessor()
        self.feature_extractor = FeatureExtractor()
        self.model_trainer = ModelTrainer()
        
        # 支持的字符集
        self.charset = string.digits + string.ascii_lowercase
        
    def load_training_data_improved(self, image_dir):
        """使用改进的处理器加载训练数据"""
        print(f"从 {image_dir} 加载训练数据...")
        
        X = []  # 特征
        y = []  # 标签
        
        # 获取所有图片文件
        image_files = [f for f in os.listdir(image_dir) 
                      if f.lower().endswith(('.png', '.jpg', '.jpeg'))]
        
        print(f"找到 {len(image_files)} 个图片文件")
        
        successful_samples = 0
        failed_samples = 0
        
        for filename in image_files:
            # 从文件名提取标签
            label = os.path.splitext(filename)[0].lower()
            
            # 验证标签
            if len(label) != 4:
                print(f"跳过 {filename}: 标签长度不是4位")
                failed_samples += 1
                continue
            
            if not all(c in self.charset for c in label):
                print(f"跳过 {filename}: 包含不支持的字符")
                failed_samples += 1
                continue
            
            image_path = os.path.join(image_dir, filename)
            
            try:
                # 使用改进的预处理
                characters, _ = self.image_processor.preprocess_image_improved(image_path)
                
                if not characters:
                    print(f"跳过 {filename}: 无法分割字符")
                    failed_samples += 1
                    continue
                
                if len(characters) != 4:
                    print(f"跳过 {filename}: 分割出{len(characters)}个字符，期望4个")
                    failed_samples += 1
                    continue
                
                # 为每个字符提取特征
                for i, char_image in enumerate(characters):
                    char_label = label[i]
                    
                    # 提取特征（确保与测试时使用相同的特征类型）
                    features = self.feature_extractor.extract_combined_features(
                        char_image, feature_types=['hog', 'contour']
                    )
                    
                    X.append(features)
                    y.append(char_label)
                
                successful_samples += 1
                
                if successful_samples % 10 == 0:
                    print(f"已处理 {successful_samples} 个成功样本...")
                    
            except Exception as e:
                print(f"处理 {filename} 时出错: {e}")
                failed_samples += 1
                continue
        
        print(f"\n数据加载完成:")
        print(f"  成功处理: {successful_samples} 个图片")
        print(f"  失败: {failed_samples} 个图片")
        print(f"  总特征样本: {len(X)} 个")
        
        return np.array(X), np.array(y)
    
    def train_and_test(self, training_dir, test_image_path, model_path):
        """训练模型并测试"""
        print("=== 使用改进处理器训练验证码识别模型 ===")
        
        # 1. 加载训练数据
        X, y = self.load_training_data_improved(training_dir)
        
        if len(X) == 0:
            print("没有可用的训练数据")
            return False
        
        print(f"\n训练数据统计:")
        print(f"  特征维度: {X.shape[1]}")
        print(f"  样本数量: {len(X)}")
        print(f"  字符类别: {len(np.unique(y))}")
        
        # 显示字符分布
        unique_chars, counts = np.unique(y, return_counts=True)
        print(f"  字符分布:")
        for char, count in zip(unique_chars, counts):
            print(f"    {char}: {count} 个样本")
        
        # 2. 更新模型训练器的字符集
        self.model_trainer.charset = self.charset
        
        # 3. 训练SVM模型
        print(f"\n开始训练SVM模型...")
        accuracy = self.model_trainer.train_svm_model(X, y)
        
        # 4. 保存模型
        os.makedirs(os.path.dirname(model_path), exist_ok=True)
        self.model_trainer.save_model(model_path)
        
        print(f"\n模型训练完成:")
        print(f"  准确率: {accuracy:.4f}")
        print(f"  模型已保存到: {model_path}")
        
        # 5. 测试模型
        if os.path.exists(test_image_path):
            print(f"\n{'='*50}")
            print(f"测试模型")
            print('='*50)
            
            result = self.test_single_image(test_image_path, model_path)
            
            if result:
                print(f"\n🎯 {test_image_path} 的识别结果: {result['result']}")
                print(f"📊 置信度: {result['confidence']:.3f}")
                print(f"🔍 字符详情:")
                for i, (char, conf) in enumerate(zip(result['result'], result['character_confidences'])):
                    print(f"    字符 {i+1}: {char} (置信度: {conf:.3f})")
            else:
                print("❌ 测试失败")
        
        return True
    
    def test_single_image(self, image_path, model_path):
        """测试单个图像"""
        print(f"测试图片: {image_path}")
        
        # 加载模型
        success = self.model_trainer.load_model(model_path)
        if not success:
            print("模型加载失败")
            return None
        
        # 预处理图像
        characters, _ = self.image_processor.preprocess_image_improved(image_path)
        
        if not characters:
            print("无法从测试图片中分割字符")
            return None
        
        print(f"分割出 {len(characters)} 个字符")
        
        # 识别每个字符
        result = ""
        confidences = []
        
        for i, char_image in enumerate(characters):
            prediction, confidence = self.model_trainer.predict_character(char_image)
            result += prediction
            confidences.append(confidence)
            print(f"字符 {i+1}: {prediction} (置信度: {confidence:.3f})")
        
        avg_confidence = np.mean(confidences) if confidences else 0.0
        
        return {
            'result': result,
            'confidence': avg_confidence,
            'character_confidences': confidences,
            'character_count': len(characters)
        }


def main():
    """主函数"""
    trainer = ImprovedCaptchaTrainer()
    
    # 配置路径
    training_dir = 'test_images'
    test_image_path = 'guess_images/image.png'
    model_path = 'models/improved_captcha_model.pkl'
    
    # 检查路径
    if not os.path.exists(training_dir):
        print(f"训练数据目录不存在: {training_dir}")
        return
    
    if not os.path.exists(test_image_path):
        print(f"测试图片不存在: {test_image_path}")
        return
    
    try:
        # 训练和测试
        success = trainer.train_and_test(training_dir, test_image_path, model_path)
        
        if success:
            print(f"\n✅ 训练和测试完成！")
            print(f"模型文件: {model_path}")
            print(f"调试图像: debug_*.png")
        else:
            print("❌ 训练失败")
            
    except Exception as e:
        print(f"运行出错: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
