import cv2
import numpy as np
from skimage.feature import hog
from skimage import exposure


class FeatureExtractor:
    """特征提取类，负责从字符图像中提取特征"""
    
    def __init__(self):
        # HOG特征参数
        self.hog_params = {
            'orientations': 9,
            'pixels_per_cell': (8, 8),
            'cells_per_block': (2, 2),
            'block_norm': 'L2-Hys',
            'visualize': False,
            'feature_vector': True
        }
    
    def extract_pixel_features(self, image):
        """提取像素特征（简单的像素值特征）"""
        # 确保图像是28x28
        if image.shape != (28, 28):
            image = cv2.resize(image, (28, 28))
        
        # 归一化像素值到0-1范围
        normalized = image.astype(np.float32) / 255.0
        
        # 展平为一维向量
        pixel_features = normalized.flatten()
        
        return pixel_features
    
    def extract_hog_features(self, image):
        """提取HOG特征"""
        # 确保图像是28x28
        if image.shape != (28, 28):
            image = cv2.resize(image, (28, 28))
        
        # 提取HOG特征
        hog_features = hog(image, **self.hog_params)
        
        return hog_features
    
    def extract_contour_features(self, image):
        """提取轮廓特征"""
        # 确保图像是二值图像
        if len(image.shape) == 3:
            image = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
        
        # 查找轮廓
        contours, _ = cv2.findContours(image, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
        
        features = []
        
        if contours:
            # 选择最大的轮廓
            largest_contour = max(contours, key=cv2.contourArea)
            
            # 轮廓面积
            area = cv2.contourArea(largest_contour)
            
            # 轮廓周长
            perimeter = cv2.arcLength(largest_contour, True)
            
            # 边界框
            x, y, w, h = cv2.boundingRect(largest_contour)
            
            # 宽高比
            aspect_ratio = float(w) / h if h != 0 else 0
            
            # 矩形度（轮廓面积与边界框面积的比值）
            rect_area = w * h
            extent = float(area) / rect_area if rect_area != 0 else 0
            
            # 凸包
            hull = cv2.convexHull(largest_contour)
            hull_area = cv2.contourArea(hull)
            
            # 凸性（轮廓面积与凸包面积的比值）
            solidity = float(area) / hull_area if hull_area != 0 else 0
            
            features = [area, perimeter, aspect_ratio, extent, solidity, w, h]
        else:
            # 如果没有找到轮廓，返回零特征
            features = [0] * 7
        
        return np.array(features)
    
    def extract_projection_features(self, image):
        """提取投影特征"""
        # 确保图像是28x28
        if image.shape != (28, 28):
            image = cv2.resize(image, (28, 28))
        
        # 二值化
        _, binary = cv2.threshold(image, 127, 255, cv2.THRESH_BINARY)
        
        # 水平投影（每行的像素和）
        horizontal_projection = np.sum(binary == 0, axis=1)  # 黑色像素计数
        
        # 垂直投影（每列的像素和）
        vertical_projection = np.sum(binary == 0, axis=0)
        
        # 归一化投影
        h_proj_norm = horizontal_projection / np.max(horizontal_projection) if np.max(horizontal_projection) > 0 else horizontal_projection
        v_proj_norm = vertical_projection / np.max(vertical_projection) if np.max(vertical_projection) > 0 else vertical_projection
        
        # 合并投影特征
        projection_features = np.concatenate([h_proj_norm, v_proj_norm])
        
        return projection_features
    
    def extract_zone_features(self, image, zones=(2, 2)):
        """提取分区特征"""
        # 确保图像是28x28
        if image.shape != (28, 28):
            image = cv2.resize(image, (28, 28))
        
        # 二值化
        _, binary = cv2.threshold(image, 127, 255, cv2.THRESH_BINARY)
        
        h_zones, w_zones = zones
        h_step = image.shape[0] // h_zones
        w_step = image.shape[1] // w_zones
        
        zone_features = []
        
        for i in range(h_zones):
            for j in range(w_zones):
                # 定义区域
                y_start = i * h_step
                y_end = (i + 1) * h_step if i < h_zones - 1 else image.shape[0]
                x_start = j * w_step
                x_end = (j + 1) * w_step if j < w_zones - 1 else image.shape[1]
                
                # 提取区域
                zone = binary[y_start:y_end, x_start:x_end]
                
                # 计算黑色像素比例
                black_pixel_ratio = np.sum(zone == 0) / zone.size
                zone_features.append(black_pixel_ratio)
        
        return np.array(zone_features)
    
    def extract_combined_features(self, image, feature_types=['pixel', 'hog', 'contour']):
        """提取组合特征"""
        features = []
        
        if 'pixel' in feature_types:
            pixel_feat = self.extract_pixel_features(image)
            features.append(pixel_feat)
        
        if 'hog' in feature_types:
            hog_feat = self.extract_hog_features(image)
            features.append(hog_feat)
        
        if 'contour' in feature_types:
            contour_feat = self.extract_contour_features(image)
            features.append(contour_feat)
        
        if 'projection' in feature_types:
            proj_feat = self.extract_projection_features(image)
            features.append(proj_feat)
        
        if 'zone' in feature_types:
            zone_feat = self.extract_zone_features(image)
            features.append(zone_feat)
        
        # 合并所有特征
        if features:
            combined_features = np.concatenate(features)
        else:
            combined_features = np.array([])
        
        return combined_features
    
    def extract_features_from_characters(self, characters, feature_types=['hog', 'contour']):
        """从字符列表中提取特征"""
        all_features = []
        
        for char_image in characters:
            features = self.extract_combined_features(char_image, feature_types)
            all_features.append(features)
        
        return all_features


if __name__ == "__main__":
    # 测试代码
    extractor = FeatureExtractor()
    
    # 创建一个测试字符图像
    test_char = np.ones((28, 28), dtype=np.uint8) * 255
    cv2.putText(test_char, 'A', (5, 20), cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 0, 0), 2)
    
    # 测试不同的特征提取方法
    print("测试特征提取...")
    
    # 像素特征
    pixel_features = extractor.extract_pixel_features(test_char)
    print(f"像素特征维度: {pixel_features.shape}")
    
    # HOG特征
    hog_features = extractor.extract_hog_features(test_char)
    print(f"HOG特征维度: {hog_features.shape}")
    
    # 轮廓特征
    contour_features = extractor.extract_contour_features(test_char)
    print(f"轮廓特征维度: {contour_features.shape}")
    
    # 投影特征
    projection_features = extractor.extract_projection_features(test_char)
    print(f"投影特征维度: {projection_features.shape}")
    
    # 分区特征
    zone_features = extractor.extract_zone_features(test_char)
    print(f"分区特征维度: {zone_features.shape}")
    
    # 组合特征
    combined_features = extractor.extract_combined_features(test_char)
    print(f"组合特征维度: {combined_features.shape}")
    
    print("特征提取测试完成！")
