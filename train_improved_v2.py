#!/usr/bin/env python3
"""
改进的训练脚本 v2 - 解决过拟合和数据质量问题
"""

import os
import numpy as np
from improved_image_processor import ImprovedImageProcessor
from feature_extractor import FeatureExtractor
from model_trainer import ModelTrainer
import string
from sklearn.model_selection import train_test_split, cross_val_score
from sklearn.preprocessing import StandardScaler
from sklearn.svm import SVC
from sklearn.ensemble import RandomForestClassifier
from sklearn.metrics import classification_report, accuracy_score
import pickle
import random


class ImprovedCaptchaTrainerV2:
    """改进的验证码训练器 v2"""
    
    def __init__(self):
        self.image_processor = ImprovedImageProcessor()
        self.feature_extractor = FeatureExtractor()
        self.charset = string.digits + string.ascii_lowercase
        self.scaler = StandardScaler()
        self.model = None
        
    def load_and_validate_data(self, image_dir, max_samples_per_char=None):
        """加载并验证训练数据"""
        print(f"从 {image_dir} 加载训练数据...")
        
        X = []
        y = []
        
        # 获取所有图片文件
        image_files = [f for f in os.listdir(image_dir) 
                      if f.lower().endswith(('.png', '.jpg', '.jpeg'))]
        
        print(f"找到 {len(image_files)} 个图片文件")
        
        # 随机打乱文件顺序
        random.shuffle(image_files)
        
        successful_samples = 0
        failed_samples = 0
        char_counts = {char: 0 for char in self.charset}
        
        for filename in image_files:
            # 从文件名提取标签
            label = os.path.splitext(filename)[0].lower()
            
            # 验证标签
            if len(label) != 4:
                failed_samples += 1
                continue
            
            if not all(c in self.charset for c in label):
                failed_samples += 1
                continue
            
            # 检查是否需要限制样本数量
            if max_samples_per_char:
                skip = False
                for char in label:
                    if char_counts[char] >= max_samples_per_char:
                        skip = True
                        break
                if skip:
                    continue
            
            image_path = os.path.join(image_dir, filename)
            
            try:
                # 使用改进的预处理
                characters, _ = self.image_processor.preprocess_image_improved(image_path)
                
                if not characters or len(characters) != 4:
                    failed_samples += 1
                    continue
                
                # 为每个字符提取特征
                for i, char_image in enumerate(characters):
                    char_label = label[i]
                    
                    # 提取特征 - 只使用HOG特征，减少复杂度
                    features = self.feature_extractor.extract_hog_features(char_image)
                    
                    X.append(features)
                    y.append(char_label)
                    char_counts[char_label] += 1
                
                successful_samples += 1
                
                if successful_samples % 50 == 0:
                    print(f"已处理 {successful_samples} 个成功样本...")
                    
            except Exception as e:
                print(f"处理 {filename} 时出错: {e}")
                failed_samples += 1
                continue
        
        print(f"\n数据加载完成:")
        print(f"  成功处理: {successful_samples} 个图片")
        print(f"  失败: {failed_samples} 个图片")
        print(f"  总特征样本: {len(X)} 个")
        
        # 显示字符分布
        print(f"\n字符分布:")
        for char in sorted(self.charset):
            if char_counts[char] > 0:
                print(f"  {char}: {char_counts[char]} 个样本")
        
        return np.array(X), np.array(y), char_counts
    
    def train_with_validation(self, X, y, test_size=0.2):
        """使用验证集训练模型"""
        print(f"\n开始训练模型...")
        print(f"特征维度: {X.shape[1]}")
        print(f"样本数量: {len(X)}")
        
        # 分割数据
        X_train, X_test, y_train, y_test = train_test_split(
            X, y, test_size=test_size, random_state=42, stratify=None
        )
        
        print(f"训练集: {len(X_train)} 样本")
        print(f"测试集: {len(X_test)} 样本")
        
        # 标准化特征
        X_train_scaled = self.scaler.fit_transform(X_train)
        X_test_scaled = self.scaler.transform(X_test)
        
        # 尝试不同的模型参数
        models = [
            ('SVM_simple', SVC(kernel='linear', C=0.1, probability=True, random_state=42)),
            ('SVM_rbf', SVC(kernel='rbf', C=1.0, gamma='scale', probability=True, random_state=42)),
            ('RandomForest', RandomForestClassifier(n_estimators=50, max_depth=10, random_state=42))
        ]
        
        best_model = None
        best_score = 0
        best_name = ""
        
        for name, model in models:
            print(f"\n测试模型: {name}")
            
            # 交叉验证
            cv_scores = cross_val_score(model, X_train_scaled, y_train, cv=3, scoring='accuracy')
            cv_mean = np.mean(cv_scores)
            cv_std = np.std(cv_scores)
            
            print(f"  交叉验证准确率: {cv_mean:.4f} (+/- {cv_std * 2:.4f})")
            
            # 训练模型
            model.fit(X_train_scaled, y_train)
            
            # 测试集评估
            y_pred = model.predict(X_test_scaled)
            test_accuracy = accuracy_score(y_test, y_pred)
            
            print(f"  测试集准确率: {test_accuracy:.4f}")
            
            if test_accuracy > best_score:
                best_score = test_accuracy
                best_model = model
                best_name = name
        
        print(f"\n最佳模型: {best_name}")
        print(f"最佳准确率: {best_score:.4f}")
        
        self.model = best_model
        
        # 详细分类报告
        y_pred = best_model.predict(X_test_scaled)
        print(f"\n分类报告:")
        print(classification_report(y_test, y_pred))
        
        return best_score
    
    def save_model(self, model_path):
        """保存模型"""
        os.makedirs(os.path.dirname(model_path), exist_ok=True)
        
        model_data = {
            'model': self.model,
            'scaler': self.scaler,
            'charset': self.charset
        }
        
        with open(model_path, 'wb') as f:
            pickle.dump(model_data, f)
        
        print(f"模型已保存到: {model_path}")
    
    def load_model(self, model_path):
        """加载模型"""
        try:
            with open(model_path, 'rb') as f:
                model_data = pickle.load(f)
            
            self.model = model_data['model']
            self.scaler = model_data['scaler']
            self.charset = model_data['charset']
            
            print(f"模型已从 {model_path} 加载")
            return True
        except Exception as e:
            print(f"加载模型失败: {e}")
            return False
    
    def predict_character(self, char_image):
        """预测单个字符"""
        if self.model is None:
            return None, 0.0
        
        # 提取特征
        features = self.feature_extractor.extract_hog_features(char_image)
        features_scaled = self.scaler.transform([features])
        
        # 预测
        prediction = self.model.predict(features_scaled)[0]
        
        # 获取置信度
        if hasattr(self.model, 'predict_proba'):
            probabilities = self.model.predict_proba(features_scaled)[0]
            confidence = np.max(probabilities)
        else:
            confidence = 1.0
        
        return prediction, confidence
    
    def test_single_image(self, image_path, model_path=None):
        """测试单个图像"""
        if model_path and not self.model:
            if not self.load_model(model_path):
                return None
        
        # 预处理图像
        characters, _ = self.image_processor.preprocess_image_improved(image_path)
        
        if not characters:
            return None
        
        # 识别每个字符
        result = ""
        confidences = []
        
        for char_image in characters:
            prediction, confidence = self.predict_character(char_image)
            if prediction:
                result += prediction
                confidences.append(confidence)
        
        avg_confidence = np.mean(confidences) if confidences else 0.0
        
        return {
            'result': result,
            'confidence': avg_confidence,
            'character_confidences': confidences,
            'character_count': len(characters)
        }


def main():
    """主函数"""
    trainer = ImprovedCaptchaTrainerV2()
    
    # 配置
    training_dir = 'test_images'
    test_image_path = 'guess_images/image.png'
    model_path = 'models/improved_captcha_model_v2.pkl'
    
    print("=== 改进的验证码识别模型训练 v2 ===")
    
    # 1. 加载数据（限制每个字符的样本数量，防止过拟合）
    X, y, char_counts = trainer.load_and_validate_data(
        training_dir, 
        max_samples_per_char=15  # 限制每个字符最多15个样本
    )
    
    if len(X) == 0:
        print("没有可用的训练数据")
        return
    
    # 2. 训练模型
    accuracy = trainer.train_with_validation(X, y)
    
    # 3. 保存模型
    trainer.save_model(model_path)
    
    # 4. 测试
    if os.path.exists(test_image_path):
        print(f"\n测试图片: {test_image_path}")
        result = trainer.test_single_image(test_image_path, model_path)
        
        if result:
            print(f"识别结果: {result['result']}")
            print(f"置信度: {result['confidence']:.3f}")
        else:
            print("识别失败")
    
    print(f"\n✅ 训练完成！模型保存在: {model_path}")


if __name__ == "__main__":
    main()
