#!/usr/bin/env python3
"""
诊断模型问题
"""

import os
import numpy as np
import glob
from collections import Counter
from train_improved import ImprovedCaptchaTrainer
from improved_image_processor import ImprovedImageProcessor
from feature_extractor import FeatureExtractor
import matplotlib.pyplot as plt


def analyze_training_data():
    """分析训练数据质量"""
    print("=== 训练数据分析 ===")
    
    # 获取所有训练图片
    image_files = glob.glob('test_images/*.png')
    print(f"训练图片总数: {len(image_files)}")
    
    # 分析标签分布
    all_chars = []
    label_lengths = []
    
    for image_file in image_files:
        filename = os.path.basename(image_file)
        label = os.path.splitext(filename)[0].lower()
        label_lengths.append(len(label))
        all_chars.extend(list(label))
    
    # 标签长度分布
    length_counter = Counter(label_lengths)
    print(f"\n标签长度分布: {dict(length_counter)}")
    
    # 字符分布
    char_counter = Counter(all_chars)
    print(f"\n字符总数: {len(all_chars)}")
    print(f"唯一字符数: {len(char_counter)}")
    
    # 显示字符分布
    print("\n字符分布:")
    for char, count in sorted(char_counter.items()):
        print(f"  {char}: {count} 次")
    
    # 找出样本数量少的字符
    min_samples = 5
    rare_chars = [char for char, count in char_counter.items() if count < min_samples]
    if rare_chars:
        print(f"\n⚠️  样本数少于{min_samples}的字符: {rare_chars}")
    
    return char_counter


def test_segmentation_quality():
    """测试分割质量"""
    print("\n=== 分割质量测试 ===")
    
    processor = ImprovedImageProcessor()
    
    # 测试几个图片的分割
    test_files = [
        'test_images/3qh4.png',
        'test_images/image.png', 
        'test_images/m76a.png',
        'test_images/xm9h.png'
    ]
    
    for image_file in test_files:
        if os.path.exists(image_file):
            print(f"\n测试分割: {os.path.basename(image_file)}")
            characters, method = processor.preprocess_image_improved(image_file, show_steps=True)
            
            if characters:
                print(f"  分割方法: {method}")
                print(f"  分割出字符数: {len(characters)}")
                for i, char in enumerate(characters):
                    print(f"    字符{i+1}: {char.shape}")
            else:
                print("  ❌ 分割失败")


def analyze_feature_consistency():
    """分析特征一致性"""
    print("\n=== 特征一致性分析 ===")
    
    processor = ImprovedImageProcessor()
    feature_extractor = FeatureExtractor()
    
    # 测试同一字符的特征一致性
    test_char = '3'
    char_features = []
    
    # 找到包含字符'3'的图片
    image_files = glob.glob('test_images/*.png')
    count = 0
    
    for image_file in image_files:
        if count >= 5:  # 只测试5个样本
            break
            
        filename = os.path.basename(image_file)
        label = os.path.splitext(filename)[0].lower()
        
        if test_char in label:
            characters, _ = processor.preprocess_image_improved(image_file)
            if characters and len(characters) == 4:
                char_pos = label.index(test_char)
                char_image = characters[char_pos]
                
                features = feature_extractor.extract_combined_features(
                    char_image, feature_types=['hog', 'contour']
                )
                char_features.append(features)
                count += 1
                print(f"  {filename}: 字符'{test_char}'在位置{char_pos+1}, 特征维度: {len(features)}")
    
    if len(char_features) > 1:
        # 计算特征相似性
        features_array = np.array(char_features)
        mean_features = np.mean(features_array, axis=0)
        std_features = np.std(features_array, axis=0)
        
        print(f"\n字符'{test_char}'的特征统计:")
        print(f"  样本数: {len(char_features)}")
        print(f"  特征维度: {len(mean_features)}")
        print(f"  特征标准差均值: {np.mean(std_features):.4f}")
        print(f"  特征标准差最大值: {np.max(std_features):.4f}")


def compare_models():
    """比较不同训练数据量的模型效果"""
    print("\n=== 模型比较 ===")
    
    trainer = ImprovedCaptchaTrainer()
    
    # 测试当前模型
    model_path = 'models/improved_captcha_model.pkl'
    if os.path.exists(model_path):
        print("当前模型存在，测试几个样本...")
        
        test_images = [
            'guess_images/3qh4.png',
            'guess_images/m76a.png', 
            'guess_images/image.png'
        ]
        
        for test_image in test_images:
            if os.path.exists(test_image):
                result = trainer.test_single_image(test_image, model_path)
                if result:
                    true_label = os.path.splitext(os.path.basename(test_image))[0]
                    print(f"  {os.path.basename(test_image)}: {true_label} -> {result['result']} (置信度: {result['confidence']:.3f})")


def suggest_improvements():
    """建议改进方案"""
    print("\n=== 改进建议 ===")
    
    suggestions = [
        "1. 数据质量检查:",
        "   - 检查新增的训练图片是否标注正确",
        "   - 验证图片质量和分割效果",
        "   - 确保字符分布相对均匀",
        "",
        "2. 模型参数调整:",
        "   - 减少模型复杂度，防止过拟合",
        "   - 调整SVM的C参数和gamma参数",
        "   - 尝试不同的特征组合",
        "",
        "3. 训练策略优化:",
        "   - 使用交叉验证评估模型",
        "   - 分批训练，逐步增加数据",
        "   - 添加数据增强技术",
        "",
        "4. 特征工程改进:",
        "   - 尝试不同的特征提取方法",
        "   - 添加特征选择步骤",
        "   - 标准化特征处理流程"
    ]
    
    for suggestion in suggestions:
        print(suggestion)


def main():
    """主函数"""
    print("🔍 验证码识别模型诊断")
    print("=" * 50)
    
    # 1. 分析训练数据
    char_counter = analyze_training_data()
    
    # 2. 测试分割质量
    test_segmentation_quality()
    
    # 3. 分析特征一致性
    analyze_feature_consistency()
    
    # 4. 比较模型
    compare_models()
    
    # 5. 改进建议
    suggest_improvements()
    
    print("\n" + "=" * 50)
    print("诊断完成！")


if __name__ == "__main__":
    main()
