import os
import numpy as np
from image_processor import ImageProcessor
from feature_extractor import FeatureExtractor
from model_trainer import ModelTrainer


class CaptchaRecognizer:
    """验证码识别器主类"""
    
    def __init__(self, model_path='models/captcha_model.pkl'):
        self.image_processor = ImageProcessor()
        self.feature_extractor = FeatureExtractor()
        self.model_trainer = ModelTrainer()
        self.model_path = model_path
        self.is_model_loaded = False
        
        # 尝试加载预训练模型
        self.load_model()
    
    def load_model(self, model_path=None):
        """加载预训练模型"""
        if model_path:
            self.model_path = model_path
        
        if os.path.exists(self.model_path):
            self.is_model_loaded = self.model_trainer.load_model(self.model_path)
            if self.is_model_loaded:
                print(f"成功加载模型: {self.model_path}")
            else:
                print(f"加载模型失败: {self.model_path}")
        else:
            print(f"模型文件不存在: {self.model_path}")
            print("请先训练模型或提供正确的模型路径")
    
    def train_model(self, data_source='generated', data_dir=None, model_type='svm', 
                   num_samples_per_char=100):
        """训练新模型"""
        print("开始训练模型...")
        
        if data_source == 'generated':
            # 使用生成的数据训练
            X, y = self.model_trainer.generate_training_data(num_samples_per_char)
        elif data_source == 'images' and data_dir:
            # 使用图像文件训练
            X, y = self.model_trainer.load_training_data_from_images(data_dir)
        else:
            print("无效的数据源或缺少数据目录")
            return False
        
        if len(X) == 0:
            print("没有可用的训练数据")
            return False
        
        print(f"训练数据: {len(X)} 个样本, {X.shape[1]} 维特征")
        
        # 训练模型
        if model_type == 'svm':
            accuracy = self.model_trainer.train_svm_model(X, y)
        elif model_type == 'random_forest':
            accuracy = self.model_trainer.train_random_forest_model(X, y)
        else:
            print("不支持的模型类型")
            return False
        
        # 保存模型
        self.model_trainer.save_model(self.model_path)
        self.is_model_loaded = True
        
        print(f"模型训练完成，准确率: {accuracy:.4f}")
        return True
    
    def recognize(self, image_path, show_process=False):
        """识别验证码"""
        if not self.is_model_loaded:
            print("模型未加载，请先训练或加载模型")
            return None
        
        # 预处理图像
        characters, boxes = self.image_processor.preprocess_image(
            image_path, show_steps=show_process
        )
        
        if not characters:
            print("未能从图像中分割出字符")
            return None
        
        # 识别每个字符
        result = ""
        confidences = []
        
        for i, char_image in enumerate(characters):
            prediction, confidence = self.model_trainer.predict_character(char_image)
            result += prediction
            confidences.append(confidence)
            
            if show_process:
                print(f"字符 {i+1}: {prediction} (置信度: {confidence:.3f})")
        
        # 计算平均置信度
        avg_confidence = np.mean(confidences) if confidences else 0.0
        
        if show_process:
            print(f"识别结果: {result}")
            print(f"平均置信度: {avg_confidence:.3f}")
        
        return {
            'result': result,
            'confidence': avg_confidence,
            'character_confidences': confidences,
            'character_count': len(characters)
        }
    
    def recognize_batch(self, image_paths, show_process=False):
        """批量识别验证码"""
        results = []
        
        for image_path in image_paths:
            print(f"处理图像: {image_path}")
            result = self.recognize(image_path, show_process)
            results.append({
                'image_path': image_path,
                'recognition_result': result
            })
        
        return results
    
    def evaluate_model(self, test_images_dir):
        """评估模型性能"""
        if not self.is_model_loaded:
            print("模型未加载")
            return None
        
        correct_predictions = 0
        total_predictions = 0
        
        for filename in os.listdir(test_images_dir):
            if filename.lower().endswith(('.png', '.jpg', '.jpeg')):
                # 假设文件名包含正确答案（例如：1234.png）
                true_label = os.path.splitext(filename)[0]
                
                image_path = os.path.join(test_images_dir, filename)
                result = self.recognize(image_path)
                
                if result and result['result'] == true_label:
                    correct_predictions += 1
                
                total_predictions += 1
                
                print(f"文件: {filename}, 真实: {true_label}, "
                      f"预测: {result['result'] if result else 'None'}")
        
        accuracy = correct_predictions / total_predictions if total_predictions > 0 else 0
        print(f"\n模型准确率: {accuracy:.4f} ({correct_predictions}/{total_predictions})")
        
        return accuracy
    
    def get_model_info(self):
        """获取模型信息"""
        if not self.is_model_loaded:
            return "模型未加载"
        
        info = {
            'model_type': self.model_trainer.model_type,
            'charset': self.model_trainer.charset,
            'model_path': self.model_path
        }
        
        return info


def main():
    """主函数，提供命令行接口"""
    import argparse
    
    parser = argparse.ArgumentParser(description='验证码识别工具')
    parser.add_argument('--action', choices=['train', 'recognize', 'evaluate'], 
                       required=True, help='操作类型')
    parser.add_argument('--image', help='要识别的图像路径')
    parser.add_argument('--model', default='models/captcha_model.pkl', 
                       help='模型文件路径')
    parser.add_argument('--model-type', choices=['svm', 'random_forest'], 
                       default='svm', help='模型类型')
    parser.add_argument('--samples', type=int, default=100, 
                       help='每个字符的训练样本数')
    parser.add_argument('--test-dir', help='测试图像目录')
    parser.add_argument('--show-process', action='store_true', 
                       help='显示处理过程')
    
    args = parser.parse_args()
    
    # 创建识别器
    recognizer = CaptchaRecognizer(args.model)
    
    if args.action == 'train':
        print("开始训练模型...")
        success = recognizer.train_model(
            model_type=args.model_type,
            num_samples_per_char=args.samples
        )
        if success:
            print("模型训练完成")
        else:
            print("模型训练失败")
    
    elif args.action == 'recognize':
        if not args.image:
            print("请提供要识别的图像路径")
            return
        
        result = recognizer.recognize(args.image, show_process=args.show_process)
        if result:
            print(f"识别结果: {result['result']}")
            print(f"置信度: {result['confidence']:.3f}")
        else:
            print("识别失败")
    
    elif args.action == 'evaluate':
        if not args.test_dir:
            print("请提供测试图像目录")
            return
        
        accuracy = recognizer.evaluate_model(args.test_dir)
        if accuracy is not None:
            print(f"评估完成，准确率: {accuracy:.4f}")


if __name__ == "__main__":
    main()
