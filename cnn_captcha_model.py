#!/usr/bin/env python3
"""
基于CNN的验证码识别模型
"""

import os
import numpy as np
import cv2
import string
import random
from collections import Counter
import pickle
import glob

import tensorflow as tf
from tensorflow import keras
from tensorflow.keras import layers
from sklearn.model_selection import train_test_split
from sklearn.preprocessing import LabelEncoder

from improved_image_processor import ImprovedImageProcessor


class CNNCaptchaModel:
    """基于CNN的验证码识别模型"""
    
    def __init__(self, img_height=28, img_width=28, num_classes=36):
        self.img_height = img_height
        self.img_width = img_width
        self.num_classes = num_classes
        self.charset = string.digits + string.ascii_lowercase
        self.label_encoder = LabelEncoder()
        self.label_encoder.fit(list(self.charset))
        self.model = None
        self.image_processor = ImprovedImageProcessor()
        
    def create_cnn_model(self):
        """创建CNN模型"""
        model = keras.Sequential([
            # 第一个卷积块
            layers.Conv2D(32, (3, 3), activation='relu', input_shape=(self.img_height, self.img_width, 1)),
            layers.BatchNormalization(),
            layers.MaxPooling2D((2, 2)),
            layers.Dropout(0.25),
            
            # 第二个卷积块
            layers.Conv2D(64, (3, 3), activation='relu'),
            layers.BatchNormalization(),
            layers.MaxPooling2D((2, 2)),
            layers.Dropout(0.25),
            
            # 第三个卷积块
            layers.Conv2D(128, (3, 3), activation='relu'),
            layers.BatchNormalization(),
            layers.Dropout(0.25),
            
            # 全连接层
            layers.Flatten(),
            layers.Dense(512, activation='relu'),
            layers.BatchNormalization(),
            layers.Dropout(0.5),
            layers.Dense(256, activation='relu'),
            layers.Dropout(0.5),
            layers.Dense(self.num_classes, activation='softmax')
        ])
        
        model.compile(
            optimizer='adam',
            loss='sparse_categorical_crossentropy',
            metrics=['accuracy']
        )
        
        return model
    
    def preprocess_char_image(self, char_image):
        """预处理单个字符图像"""
        # 确保是灰度图
        if len(char_image.shape) == 3:
            char_image = cv2.cvtColor(char_image, cv2.COLOR_BGR2GRAY)
        
        # 调整大小
        char_image = cv2.resize(char_image, (self.img_width, self.img_height))
        
        # 归一化到0-1
        char_image = char_image.astype(np.float32) / 255.0
        
        # 添加通道维度
        char_image = np.expand_dims(char_image, axis=-1)
        
        return char_image
    
    def load_training_data(self, image_dir, max_samples_per_char=20):
        """加载训练数据"""
        print(f"从 {image_dir} 加载CNN训练数据...")
        
        X = []
        y = []
        
        # 获取所有图片文件
        image_files = [f for f in os.listdir(image_dir) 
                      if f.lower().endswith(('.png', '.jpg', '.jpeg'))]
        
        print(f"找到 {len(image_files)} 个图片文件")
        
        # 随机打乱文件顺序
        random.shuffle(image_files)
        
        successful_samples = 0
        failed_samples = 0
        char_counts = {char: 0 for char in self.charset}
        
        for filename in image_files:
            # 从文件名提取标签
            label = os.path.splitext(filename)[0].lower()
            
            # 验证标签
            if len(label) != 4:
                failed_samples += 1
                continue
            
            if not all(c in self.charset for c in label):
                failed_samples += 1
                continue
            
            # 检查是否需要限制样本数量
            if max_samples_per_char:
                skip = False
                for char in label:
                    if char_counts[char] >= max_samples_per_char:
                        skip = True
                        break
                if skip:
                    continue
            
            image_path = os.path.join(image_dir, filename)
            
            try:
                # 使用改进的预处理分割字符
                characters, _ = self.image_processor.preprocess_image_improved(image_path)
                
                if not characters or len(characters) != 4:
                    failed_samples += 1
                    continue
                
                # 为每个字符准备数据
                for i, char_image in enumerate(characters):
                    char_label = label[i]
                    
                    # 预处理字符图像
                    processed_char = self.preprocess_char_image(char_image)
                    
                    # 编码标签
                    encoded_label = self.label_encoder.transform([char_label])[0]
                    
                    X.append(processed_char)
                    y.append(encoded_label)
                    char_counts[char_label] += 1
                
                successful_samples += 1
                
                if successful_samples % 50 == 0:
                    print(f"已处理 {successful_samples} 个成功样本...")
                    
            except Exception as e:
                print(f"处理 {filename} 时出错: {e}")
                failed_samples += 1
                continue
        
        print(f"\nCNN数据加载完成:")
        print(f"  成功处理: {successful_samples} 个图片")
        print(f"  失败: {failed_samples} 个图片")
        print(f"  总字符样本: {len(X)} 个")
        
        # 显示字符分布
        print(f"\n字符分布:")
        for char in sorted(self.charset):
            if char_counts[char] > 0:
                print(f"  {char}: {char_counts[char]} 个样本")
        
        return np.array(X), np.array(y), char_counts
    
    def train_model(self, X, y, validation_split=0.2, epochs=50, batch_size=32):
        """训练CNN模型"""
        print(f"\n开始训练CNN模型...")
        print(f"数据形状: {X.shape}")
        print(f"标签数量: {len(y)}")
        
        # 创建模型
        self.model = self.create_cnn_model()
        
        print("\n模型结构:")
        self.model.summary()
        
        # 设置回调函数
        callbacks = [
            keras.callbacks.EarlyStopping(
                monitor='val_accuracy',
                patience=10,
                restore_best_weights=True
            ),
            keras.callbacks.ReduceLROnPlateau(
                monitor='val_loss',
                factor=0.5,
                patience=5,
                min_lr=1e-7
            )
        ]
        
        # 训练模型
        history = self.model.fit(
            X, y,
            validation_split=validation_split,
            epochs=epochs,
            batch_size=batch_size,
            callbacks=callbacks,
            verbose=1
        )
        
        # 获取最终准确率
        final_accuracy = max(history.history['val_accuracy'])
        print(f"\n训练完成！最佳验证准确率: {final_accuracy:.4f}")
        
        return history
    
    def save_model(self, model_path):
        """保存模型"""
        os.makedirs(os.path.dirname(model_path), exist_ok=True)
        
        # 保存Keras模型
        model_file = model_path.replace('.pkl', '.h5')
        self.model.save(model_file)
        
        # 保存标签编码器和其他配置
        config = {
            'label_encoder': self.label_encoder,
            'charset': self.charset,
            'img_height': self.img_height,
            'img_width': self.img_width,
            'num_classes': self.num_classes
        }
        
        with open(model_path, 'wb') as f:
            pickle.dump(config, f)
        
        print(f"CNN模型已保存:")
        print(f"  模型文件: {model_file}")
        print(f"  配置文件: {model_path}")
    
    def load_model(self, model_path):
        """加载模型"""
        try:
            # 加载配置
            with open(model_path, 'rb') as f:
                config = pickle.load(f)
            
            self.label_encoder = config['label_encoder']
            self.charset = config['charset']
            self.img_height = config['img_height']
            self.img_width = config['img_width']
            self.num_classes = config['num_classes']
            
            # 加载Keras模型
            model_file = model_path.replace('.pkl', '.h5')
            self.model = keras.models.load_model(model_file)
            
            print(f"CNN模型已加载: {model_path}")
            return True
        except Exception as e:
            print(f"加载CNN模型失败: {e}")
            return False
    
    def predict_character(self, char_image):
        """预测单个字符"""
        if self.model is None:
            return None, 0.0
        
        # 预处理图像
        processed_char = self.preprocess_char_image(char_image)
        processed_char = np.expand_dims(processed_char, axis=0)
        
        # 预测
        predictions = self.model.predict(processed_char, verbose=0)
        predicted_class = np.argmax(predictions[0])
        confidence = np.max(predictions[0])
        
        # 解码标签
        predicted_char = self.label_encoder.inverse_transform([predicted_class])[0]
        
        return predicted_char, confidence
    
    def predict_captcha(self, image_path):
        """预测整个验证码"""
        if self.model is None:
            return None
        
        # 分割字符
        characters, _ = self.image_processor.preprocess_image_improved(image_path)
        
        if not characters:
            return None
        
        # 预测每个字符
        result = ""
        confidences = []
        
        for char_image in characters:
            predicted_char, confidence = self.predict_character(char_image)
            if predicted_char:
                result += predicted_char
                confidences.append(confidence)
        
        avg_confidence = np.mean(confidences) if confidences else 0.0
        
        return {
            'result': result,
            'confidence': avg_confidence,
            'character_confidences': confidences,
            'character_count': len(characters)
        }


def main():
    """主函数"""
    print("=== 基于CNN的验证码识别模型训练 ===")
    
    # 初始化模型
    cnn_model = CNNCaptchaModel()
    
    # 配置
    training_dir = 'test_images'
    test_image_path = 'guess_images/image.png'
    model_path = 'models/cnn_captcha_model.pkl'
    
    # 1. 加载训练数据
    X, y, char_counts = cnn_model.load_training_data(
        training_dir, 
        max_samples_per_char=25  # 每个字符最多25个样本
    )
    
    if len(X) == 0:
        print("没有可用的训练数据")
        return
    
    # 2. 训练模型
    history = cnn_model.train_model(X, y, epochs=100, batch_size=64)
    
    # 3. 保存模型
    cnn_model.save_model(model_path)
    
    # 4. 测试单个图片
    if os.path.exists(test_image_path):
        print(f"\n测试图片: {test_image_path}")
        result = cnn_model.predict_captcha(test_image_path)
        
        if result:
            print(f"识别结果: {result['result']}")
            print(f"置信度: {result['confidence']:.3f}")
            for i, (char, conf) in enumerate(zip(result['result'], result['character_confidences'])):
                print(f"  字符{i+1}: {char} (置信度: {conf:.3f})")
        else:
            print("识别失败")
    
    print(f"\n✅ CNN模型训练完成！")
    print(f"模型文件: {model_path}")


if __name__ == "__main__":
    main()
