#!/usr/bin/env python3
"""
改进的图像处理器，专门处理连接字符的验证码
"""

import cv2
import numpy as np
from PIL import Image
from image_processor import ImageProcessor


class ImprovedImageProcessor(ImageProcessor):
    """改进的图像处理器，能够处理连接字符的验证码"""
    
    def __init__(self):
        super().__init__()
    
    def segment_connected_characters(self, image, binary_image, expected_chars=4):
        """分割连接的字符"""
        print(f"尝试分割连接字符，期望 {expected_chars} 个字符")
        
        # 方法1: 垂直投影分割
        chars_method1 = self.segment_by_vertical_projection(binary_image, expected_chars)
        
        # 方法2: 固定宽度分割
        chars_method2 = self.segment_by_fixed_width(binary_image, expected_chars)
        
        # 方法3: 轮廓分析 + 分割
        chars_method3 = self.segment_by_contour_analysis(binary_image, expected_chars)
        
        # 选择最佳结果
        methods = [
            (chars_method1, "垂直投影"),
            (chars_method2, "固定宽度"),
            (chars_method3, "轮廓分析")
        ]
        
        best_chars = None
        best_method = None
        
        for chars, method_name in methods:
            if chars and len(chars) == expected_chars:
                print(f"✓ {method_name} 方法成功分割出 {len(chars)} 个字符")
                best_chars = chars
                best_method = method_name
                break
            elif chars:
                print(f"  {method_name} 方法分割出 {len(chars)} 个字符")
        
        if not best_chars:
            # 如果都失败了，选择字符数最接近期望的
            valid_methods = [(chars, name) for chars, name in methods if chars]
            if valid_methods:
                best_chars, best_method = min(valid_methods, 
                                            key=lambda x: abs(len(x[0]) - expected_chars))
                print(f"选择最佳方法: {best_method}，分割出 {len(best_chars)} 个字符")
        
        return best_chars, best_method
    
    def segment_by_vertical_projection(self, binary_image, expected_chars=4):
        """使用垂直投影分割字符"""
        # 计算垂直投影
        height, width = binary_image.shape
        vertical_projection = np.sum(binary_image == 0, axis=0)  # 黑色像素计数
        
        # 平滑投影
        kernel_size = max(1, width // 50)
        if kernel_size > 1:
            kernel = np.ones(kernel_size) / kernel_size
            vertical_projection = np.convolve(vertical_projection, kernel, mode='same')
        
        # 找到分割点
        # 寻找投影的局部最小值
        min_threshold = np.max(vertical_projection) * 0.1  # 最小阈值
        
        # 找到可能的分割点
        split_points = []
        for i in range(1, len(vertical_projection) - 1):
            if (vertical_projection[i] < vertical_projection[i-1] and 
                vertical_projection[i] < vertical_projection[i+1] and
                vertical_projection[i] < min_threshold):
                split_points.append(i)
        
        # 如果分割点太多，选择最明显的几个
        if len(split_points) > expected_chars - 1:
            # 按投影值排序，选择最小的几个
            split_points = sorted(split_points, key=lambda x: vertical_projection[x])
            split_points = split_points[:expected_chars-1]
            split_points.sort()
        
        # 如果分割点不够，尝试均匀分割
        if len(split_points) < expected_chars - 1:
            char_width = width // expected_chars
            split_points = [char_width * (i + 1) for i in range(expected_chars - 1)]
        
        # 分割字符
        characters = []
        start_x = 0
        
        for split_x in split_points + [width]:
            if split_x > start_x:
                char_image = binary_image[:, start_x:split_x]
                if char_image.shape[1] > 0:  # 确保有宽度
                    characters.append(char_image)
                start_x = split_x
        
        return characters
    
    def segment_by_fixed_width(self, binary_image, expected_chars=4):
        """使用固定宽度分割字符"""
        height, width = binary_image.shape
        char_width = width // expected_chars
        
        characters = []
        for i in range(expected_chars):
            start_x = i * char_width
            end_x = (i + 1) * char_width if i < expected_chars - 1 else width
            
            char_image = binary_image[:, start_x:end_x]
            characters.append(char_image)
        
        return characters
    
    def segment_by_contour_analysis(self, binary_image, expected_chars=4):
        """使用轮廓分析分割字符"""
        # 查找轮廓
        contours, _ = cv2.findContours(binary_image, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
        
        if not contours:
            return None
        
        # 如果只有一个大轮廓，尝试分割它
        if len(contours) == 1:
            # 获取轮廓的边界框
            x, y, w, h = cv2.boundingRect(contours[0])
            
            # 在轮廓区域内进行分割
            roi = binary_image[y:y+h, x:x+w]
            
            # 使用垂直投影在ROI内分割
            chars = self.segment_by_vertical_projection(roi, expected_chars)
            
            return chars
        
        # 如果有多个轮廓，按x坐标排序
        elif len(contours) > 1:
            # 获取边界框并排序
            bounding_boxes = []
            for contour in contours:
                x, y, w, h = cv2.boundingRect(contour)
                if w > 5 and h > 5:  # 过滤太小的轮廓
                    bounding_boxes.append((x, y, w, h))
            
            # 按x坐标排序
            bounding_boxes.sort(key=lambda box: box[0])
            
            # 提取字符
            characters = []
            for x, y, w, h in bounding_boxes:
                char_image = binary_image[y:y+h, x:x+w]
                characters.append(char_image)
            
            return characters
        
        return None
    
    def preprocess_image_improved(self, image_path, show_steps=False):
        """改进的图像预处理流程"""
        print(f"处理图像: {image_path}")
        
        # 1. 加载图像
        image = self.load_image(image_path)
        if image is None:
            return None, None
        
        print(f"图像尺寸: {image.shape}")
        
        # 2. 转换为灰度图
        gray = self.to_grayscale(image)
        
        # 3. 降噪
        denoised = self.denoise(gray)
        
        # 4. 尝试不同的二值化方法
        binary_methods = [
            ('otsu', self.binarize(denoised, method='otsu')),
            ('adaptive', self.binarize(denoised, method='adaptive')),
            ('fixed', self.binarize(denoised, method='fixed'))
        ]
        
        best_characters = None
        best_method = None
        best_binary = None
        
        for method_name, binary in binary_methods:
            print(f"\n尝试 {method_name} 二值化...")
            
            # 形态学处理
            cleaned = self.remove_noise_morphology(binary)
            
            # 尝试分割连接字符
            characters, seg_method = self.segment_connected_characters(image, cleaned, expected_chars=4)
            
            if characters and len(characters) == 4:
                print(f"✓ {method_name} + {seg_method} 成功分割出4个字符！")
                best_characters = characters
                best_method = f"{method_name} + {seg_method}"
                best_binary = cleaned
                break
            elif characters and (not best_characters or len(characters) > len(best_characters)):
                best_characters = characters
                best_method = f"{method_name} + {seg_method}"
                best_binary = cleaned
        
        if not best_characters:
            print("所有方法都无法分割字符")
            return None, None
        
        print(f"\n最终选择: {best_method}，分割出 {len(best_characters)} 个字符")
        
        # 调整字符大小
        resized_chars = []
        for i, char in enumerate(best_characters):
            resized_char = self.resize_character(char)
            resized_chars.append(resized_char)
            print(f"字符 {i+1} 尺寸: {char.shape} -> {resized_char.shape}")
        
        # 保存分割结果图像用于调试
        if show_steps:
            self.save_segmentation_debug(image, best_binary, best_characters, image_path)
        
        return resized_chars, None
    
    def save_segmentation_debug(self, original, binary, characters, image_path):
        """保存分割调试图像"""
        import os
        
        # 创建调试图像
        debug_height = max(original.shape[0], 28)
        debug_width = original.shape[1] + binary.shape[1] + len(characters) * 30
        debug_image = np.ones((debug_height, debug_width, 3), dtype=np.uint8) * 255
        
        # 放置原图
        debug_image[:original.shape[0], :original.shape[1]] = original
        
        # 放置二值图
        start_x = original.shape[1] + 10
        binary_rgb = cv2.cvtColor(binary, cv2.COLOR_GRAY2RGB)
        debug_image[:binary.shape[0], start_x:start_x+binary.shape[1]] = binary_rgb
        
        # 放置分割的字符
        start_x = original.shape[1] + binary.shape[1] + 20
        for i, char in enumerate(characters):
            char_rgb = cv2.cvtColor(char, cv2.COLOR_GRAY2RGB)
            char_resized = cv2.resize(char_rgb, (28, 28))

            y_offset = (debug_height - 28) // 2
            end_x = min(start_x + 28, debug_width)
            if end_x > start_x:
                debug_image[y_offset:y_offset+28, start_x:end_x] = char_resized[:, :end_x-start_x]
            start_x += 30
        
        # 保存调试图像
        debug_filename = f"debug_{os.path.basename(image_path)}"
        cv2.imwrite(debug_filename, debug_image)
        print(f"调试图像已保存: {debug_filename}")


def test_improved_processor():
    """测试改进的处理器"""
    processor = ImprovedImageProcessor()
    
    # 测试几个图片
    test_images = [
        'test_images/qnjz.png',
        'test_images/2ivu.png',
        'guess_images/image.png'
    ]
    
    for image_path in test_images:
        if os.path.exists(image_path):
            print(f"\n{'='*50}")
            print(f"测试图片: {image_path}")
            print('='*50)
            
            characters, _ = processor.preprocess_image_improved(image_path, show_steps=True)
            
            if characters:
                print(f"成功分割出 {len(characters)} 个字符")
            else:
                print("分割失败")


if __name__ == "__main__":
    import os
    test_improved_processor()
