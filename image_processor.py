import cv2
import numpy as np
from PIL import Image
import matplotlib.pyplot as plt


class ImageProcessor:
    """图像预处理类，负责验证码图像的预处理操作"""
    
    def __init__(self):
        pass
    
    def load_image(self, image_path):
        """加载图像"""
        try:
            # 使用PIL加载图像
            pil_image = Image.open(image_path)
            # 转换为numpy数组
            image = np.array(pil_image)
            # 如果是RGBA，转换为RGB
            if len(image.shape) == 3 and image.shape[2] == 4:
                image = cv2.cvtColor(image, cv2.COLOR_RGBA2RGB)
            return image
        except Exception as e:
            print(f"加载图像失败: {e}")
            return None
    
    def to_grayscale(self, image):
        """转换为灰度图像"""
        if len(image.shape) == 3:
            return cv2.cvtColor(image, cv2.COLOR_RGB2GRAY)
        return image
    
    def denoise(self, image):
        """图像降噪"""
        # 使用高斯滤波降噪
        denoised = cv2.GaussianBlur(image, (3, 3), 0)
        return denoised
    
    def binarize(self, image, method='otsu'):
        """图像二值化"""
        if method == 'otsu':
            # 使用OTSU自适应阈值
            _, binary = cv2.threshold(image, 0, 255, cv2.THRESH_BINARY + cv2.THRESH_OTSU)
        elif method == 'adaptive':
            # 使用自适应阈值
            binary = cv2.adaptiveThreshold(image, 255, cv2.ADAPTIVE_THRESH_GAUSSIAN_C, 
                                         cv2.THRESH_BINARY, 11, 2)
        else:
            # 使用固定阈值
            _, binary = cv2.threshold(image, 127, 255, cv2.THRESH_BINARY)
        
        return binary
    
    def remove_noise_morphology(self, binary_image):
        """使用形态学操作去除噪声"""
        # 定义结构元素
        kernel = np.ones((2, 2), np.uint8)
        
        # 开运算去除小噪点
        cleaned = cv2.morphologyEx(binary_image, cv2.MORPH_OPEN, kernel)
        
        # 闭运算填充字符内部的小洞
        cleaned = cv2.morphologyEx(cleaned, cv2.MORPH_CLOSE, kernel)
        
        return cleaned
    
    def find_character_contours(self, binary_image):
        """查找字符轮廓"""
        # 查找轮廓
        contours, _ = cv2.findContours(binary_image, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
        
        # 过滤轮廓，去除太小的噪声
        min_area = 50  # 最小面积阈值
        filtered_contours = []
        
        for contour in contours:
            area = cv2.contourArea(contour)
            if area > min_area:
                filtered_contours.append(contour)
        
        return filtered_contours
    
    def segment_characters(self, image, binary_image):
        """分割字符"""
        contours = self.find_character_contours(binary_image)
        
        # 获取边界框
        bounding_boxes = []
        for contour in contours:
            x, y, w, h = cv2.boundingRect(contour)
            bounding_boxes.append((x, y, w, h))
        
        # 按x坐标排序，确保字符顺序正确
        bounding_boxes.sort(key=lambda box: box[0])
        
        # 提取字符图像
        characters = []
        for x, y, w, h in bounding_boxes:
            # 添加一些边距
            margin = 2
            x_start = max(0, x - margin)
            y_start = max(0, y - margin)
            x_end = min(image.shape[1], x + w + margin)
            y_end = min(image.shape[0], y + h + margin)
            
            char_image = binary_image[y_start:y_end, x_start:x_end]
            characters.append(char_image)
        
        return characters, bounding_boxes
    
    def resize_character(self, char_image, target_size=(28, 28)):
        """调整字符图像大小"""
        return cv2.resize(char_image, target_size, interpolation=cv2.INTER_AREA)
    
    def preprocess_image(self, image_path, show_steps=False):
        """完整的图像预处理流程"""
        # 1. 加载图像
        image = self.load_image(image_path)
        if image is None:
            return None, None
        
        # 2. 转换为灰度图
        gray = self.to_grayscale(image)
        
        # 3. 降噪
        denoised = self.denoise(gray)
        
        # 4. 二值化
        binary = self.binarize(denoised, method='otsu')
        
        # 5. 形态学去噪
        cleaned = self.remove_noise_morphology(binary)
        
        # 6. 字符分割
        characters, boxes = self.segment_characters(image, cleaned)
        
        # 7. 调整字符大小
        resized_chars = []
        for char in characters:
            resized_char = self.resize_character(char)
            resized_chars.append(resized_char)
        
        if show_steps:
            self.show_processing_steps(image, gray, denoised, binary, cleaned, characters)
        
        return resized_chars, boxes
    
    def show_processing_steps(self, original, gray, denoised, binary, cleaned, characters):
        """显示处理步骤"""
        fig, axes = plt.subplots(2, 3, figsize=(15, 10))
        
        axes[0, 0].imshow(original)
        axes[0, 0].set_title('原始图像')
        axes[0, 0].axis('off')
        
        axes[0, 1].imshow(gray, cmap='gray')
        axes[0, 1].set_title('灰度图像')
        axes[0, 1].axis('off')
        
        axes[0, 2].imshow(denoised, cmap='gray')
        axes[0, 2].set_title('降噪后')
        axes[0, 2].axis('off')
        
        axes[1, 0].imshow(binary, cmap='gray')
        axes[1, 0].set_title('二值化')
        axes[1, 0].axis('off')
        
        axes[1, 1].imshow(cleaned, cmap='gray')
        axes[1, 1].set_title('形态学处理')
        axes[1, 1].axis('off')
        
        # 显示分割的字符
        if characters:
            combined_chars = np.hstack(characters) if len(characters) > 1 else characters[0]
            axes[1, 2].imshow(combined_chars, cmap='gray')
            axes[1, 2].set_title(f'分割字符 ({len(characters)}个)')
        else:
            axes[1, 2].text(0.5, 0.5, '未检测到字符', ha='center', va='center')
            axes[1, 2].set_title('分割字符')
        axes[1, 2].axis('off')
        
        plt.tight_layout()
        plt.show()


if __name__ == "__main__":
    # 测试代码
    processor = ImageProcessor()
    
    # 创建一个简单的测试图像
    test_image = np.ones((50, 200, 3), dtype=np.uint8) * 255
    cv2.putText(test_image, 'TEST', (10, 35), cv2.FONT_HERSHEY_SIMPLEX, 1, (0, 0, 0), 2)
    
    # 保存测试图像
    cv2.imwrite('test_images/test_captcha.png', test_image)
    
    # 测试预处理
    characters, boxes = processor.preprocess_image('test_images/test_captcha.png', show_steps=True)
    
    if characters:
        print(f"成功分割出 {len(characters)} 个字符")
        for i, char in enumerate(characters):
            print(f"字符 {i+1} 尺寸: {char.shape}")
    else:
        print("未能分割出字符")
