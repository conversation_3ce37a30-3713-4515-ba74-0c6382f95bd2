#!/usr/bin/env python3
"""
从真实验证码图片训练模型
"""

import os
import numpy as np
import cv2
from image_processor import ImageProcessor
from feature_extractor import FeatureExtractor
from model_trainer import ModelTrainer
import string


class RealCaptchaTrainer:
    """从真实验证码图片训练模型"""
    
    def __init__(self):
        self.image_processor = ImageProcessor()
        self.feature_extractor = FeatureExtractor()
        self.model_trainer = ModelTrainer()
        
        # 支持的字符集（根据你的验证码调整）
        self.charset = string.digits + string.ascii_lowercase
        
    def load_training_data_from_directory(self, image_dir):
        """从目录加载训练数据"""
        print(f"从 {image_dir} 加载训练数据...")
        
        X = []  # 特征
        y = []  # 标签
        
        # 获取所有图片文件
        image_files = [f for f in os.listdir(image_dir) 
                      if f.lower().endswith(('.png', '.jpg', '.jpeg'))]
        
        print(f"找到 {len(image_files)} 个图片文件")
        
        successful_samples = 0
        failed_samples = 0
        
        for filename in image_files:
            # 从文件名提取标签（去掉.png扩展名）
            label = os.path.splitext(filename)[0].lower()
            
            # 验证标签长度
            if len(label) != 4:
                print(f"跳过 {filename}: 标签长度不是4位")
                failed_samples += 1
                continue
            
            # 验证标签字符
            if not all(c in self.charset for c in label):
                print(f"跳过 {filename}: 包含不支持的字符")
                failed_samples += 1
                continue
            
            image_path = os.path.join(image_dir, filename)
            
            try:
                # 预处理图像
                characters, boxes = self.image_processor.preprocess_image(image_path)
                
                if not characters:
                    print(f"跳过 {filename}: 无法分割字符")
                    failed_samples += 1
                    continue
                
                # 检查分割的字符数量是否匹配标签
                if len(characters) != len(label):
                    print(f"跳过 {filename}: 分割出{len(characters)}个字符，但标签有{len(label)}个字符")
                    failed_samples += 1
                    continue
                
                # 为每个字符提取特征
                for i, char_image in enumerate(characters):
                    char_label = label[i]
                    
                    # 提取特征
                    features = self.feature_extractor.extract_combined_features(
                        char_image, feature_types=['hog', 'contour', 'projection']
                    )
                    
                    X.append(features)
                    y.append(char_label)
                
                successful_samples += 1
                
                if successful_samples % 10 == 0:
                    print(f"已处理 {successful_samples} 个成功样本...")
                    
            except Exception as e:
                print(f"处理 {filename} 时出错: {e}")
                failed_samples += 1
                continue
        
        print(f"数据加载完成:")
        print(f"  成功处理: {successful_samples} 个图片")
        print(f"  失败: {failed_samples} 个图片")
        print(f"  总特征样本: {len(X)} 个")
        
        return np.array(X), np.array(y)
    
    def train_model_from_images(self, image_dir, model_path, model_type='svm'):
        """从图片训练模型"""
        print("=== 从真实验证码图片训练模型 ===")
        
        # 加载训练数据
        X, y = self.load_training_data_from_directory(image_dir)
        
        if len(X) == 0:
            print("没有可用的训练数据")
            return False
        
        print(f"训练数据统计:")
        print(f"  特征维度: {X.shape[1]}")
        print(f"  样本数量: {len(X)}")
        print(f"  字符类别: {len(np.unique(y))}")
        
        # 显示字符分布
        unique_chars, counts = np.unique(y, return_counts=True)
        print(f"  字符分布:")
        for char, count in zip(unique_chars, counts):
            print(f"    {char}: {count} 个样本")
        
        # 更新模型训练器的字符集
        self.model_trainer.charset = self.charset
        
        # 训练模型
        if model_type == 'svm':
            accuracy = self.model_trainer.train_svm_model(X, y)
        elif model_type == 'random_forest':
            accuracy = self.model_trainer.train_random_forest_model(X, y)
        else:
            print(f"不支持的模型类型: {model_type}")
            return False
        
        # 保存模型
        self.model_trainer.save_model(model_path)
        
        print(f"模型训练完成，准确率: {accuracy:.4f}")
        print(f"模型已保存到: {model_path}")
        
        return True
    
    def test_model(self, model_path, test_image_path):
        """测试模型"""
        print(f"\n=== 测试模型 ===")
        
        # 加载模型
        success = self.model_trainer.load_model(model_path)
        if not success:
            print("模型加载失败")
            return None
        
        print(f"测试图片: {test_image_path}")
        
        # 预处理图像
        characters, boxes = self.image_processor.preprocess_image(test_image_path, show_steps=False)
        
        if not characters:
            print("无法从测试图片中分割字符")
            return None
        
        print(f"分割出 {len(characters)} 个字符")
        
        # 识别每个字符
        result = ""
        confidences = []
        
        for i, char_image in enumerate(characters):
            prediction, confidence = self.model_trainer.predict_character(char_image)
            result += prediction
            confidences.append(confidence)
            print(f"字符 {i+1}: {prediction} (置信度: {confidence:.3f})")
        
        avg_confidence = np.mean(confidences) if confidences else 0.0
        
        print(f"\n最终识别结果: {result}")
        print(f"平均置信度: {avg_confidence:.3f}")
        
        return {
            'result': result,
            'confidence': avg_confidence,
            'character_confidences': confidences,
            'character_count': len(characters)
        }


def main():
    """主函数"""
    trainer = RealCaptchaTrainer()
    
    # 训练数据目录
    training_dir = 'test_images'
    
    # 模型保存路径
    model_path = 'models/real_captcha_model.pkl'
    
    # 测试图片路径
    test_image_path = 'guess_images/image.png'
    
    # 检查目录和文件是否存在
    if not os.path.exists(training_dir):
        print(f"训练数据目录不存在: {training_dir}")
        return
    
    if not os.path.exists(test_image_path):
        print(f"测试图片不存在: {test_image_path}")
        return
    
    # 创建模型目录
    os.makedirs('models', exist_ok=True)
    
    try:
        # 训练模型
        print("开始训练...")
        success = trainer.train_model_from_images(training_dir, model_path, model_type='svm')
        
        if success:
            print("\n" + "="*50)
            # 测试模型
            result = trainer.test_model(model_path, test_image_path)
            
            if result:
                print(f"\n🎯 guess_images/image.png 的识别结果: {result['result']}")
                print(f"📊 置信度: {result['confidence']:.3f}")
            else:
                print("❌ 测试失败")
        else:
            print("❌ 训练失败")
            
    except Exception as e:
        print(f"运行出错: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
