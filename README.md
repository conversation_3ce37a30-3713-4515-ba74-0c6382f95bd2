# 验证码识别工具

一个基于传统计算机视觉技术的验证码识别工具，不依赖外部大模型。

## 功能特点

- 支持4位字符验证码识别（数字0-9和大写字母A-Z）
- 基于OpenCV和scikit-learn
- 使用传统机器学习算法（SVM/随机森林）
- 包含完整的图像预处理流程
- 可训练自定义模型
- 支持命令行和Python API两种使用方式

## 安装依赖

```bash
pip install -r requirements.txt
```

## 快速开始

### 1. 运行演示

```bash
# 完整演示（包括模型训练）
python demo.py --full

# 快速识别演示
python demo.py --quick

# 交互式演示
python demo.py --interactive
```

### 2. 命令行使用

```bash
# 训练模型
python captcha_recognizer.py --action train --model models/my_model.pkl --model-type svm --samples 100

# 识别验证码
python captcha_recognizer.py --action recognize --image test_images/captcha.png --model models/my_model.pkl

# 显示处理过程
python captcha_recognizer.py --action recognize --image test_images/captcha.png --model models/my_model.pkl --show-process
```

### 3. Python API使用

```python
from captcha_recognizer import CaptchaRecognizer

# 初始化识别器
recognizer = CaptchaRecognizer('models/my_model.pkl')

# 训练模型（如果没有预训练模型）
recognizer.train_model(model_type='svm', num_samples_per_char=100)

# 识别验证码
result = recognizer.recognize('path/to/captcha.png')
if result:
    print(f"识别结果: {result['result']}")
    print(f"置信度: {result['confidence']:.3f}")
```

## 项目结构

```
├── captcha_recognizer.py    # 主识别程序
├── image_processor.py       # 图像预处理模块
├── feature_extractor.py     # 特征提取模块
├── model_trainer.py         # 模型训练模块
├── utils.py                 # 工具函数和验证码生成器
├── demo.py                  # 演示脚本
├── test_captcha.py          # 测试脚本
├── models/                  # 训练好的模型文件
├── test_images/             # 测试图片
└── examples/                # 使用示例
```

## 技术原理

### 图像预处理
1. 灰度化转换
2. 高斯滤波降噪
3. OTSU自适应二值化
4. 形态学操作去除噪声
5. 轮廓检测和字符分割
6. 字符图像标准化（28x28像素）

### 特征提取
- **HOG特征**: 方向梯度直方图，捕捉字符的边缘和形状信息
- **轮廓特征**: 面积、周长、宽高比、矩形度、凸性等几何特征
- **投影特征**: 水平和垂直投影，反映字符的分布特征
- **分区特征**: 将字符分成多个区域，计算每个区域的像素密度

### 机器学习模型
- **SVM**: 支持向量机，适合小样本分类
- **随机森林**: 集成学习方法，具有较好的泛化能力

## 性能优化建议

1. **增加训练样本**: 每个字符使用更多训练样本（推荐200-500个）
2. **特征工程**: 根据具体验证码类型调整特征提取方法
3. **数据增强**: 添加旋转、缩放、噪声等数据增强技术
4. **模型调优**: 使用网格搜索优化模型超参数

## 测试

```bash
# 运行快速测试
python test_captcha.py --quick

# 运行完整单元测试
python test_captcha.py --full
```

## 使用限制

- 目前仅支持4位字符的验证码
- 支持的字符集：数字0-9和大写字母A-Z
- 适用于相对简单的验证码，复杂的扭曲、重叠字符可能识别效果不佳
- 需要足够的训练数据才能获得较好的识别准确率

## 注意事项

1. **训练时间**: 首次训练模型可能需要几分钟时间
2. **模型大小**: 训练好的模型文件通常在几MB到几十MB
3. **准确率**: 在简单验证码上可达到70-90%的准确率，复杂验证码需要更多优化
4. **内存使用**: 训练过程中会占用一定内存，建议在内存充足的环境下运行

## 扩展功能

可以通过以下方式扩展功能：

1. **支持更多字符**: 修改`ModelTrainer`中的`charset`变量
2. **支持不同长度**: 调整字符分割和识别逻辑
3. **添加新特征**: 在`FeatureExtractor`中实现新的特征提取方法
4. **使用深度学习**: 可以替换为CNN等深度学习模型

## 许可证

本项目仅供学习和研究使用。

## 贡献

欢迎提交Issue和Pull Request来改进这个项目。
