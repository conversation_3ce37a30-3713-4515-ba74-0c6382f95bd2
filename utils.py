import cv2
import numpy as np
import os
import string
import random
from PIL import Image, ImageDraw, ImageFont


class CaptchaGenerator:
    """验证码生成器，用于创建测试数据"""
    
    def __init__(self, width=120, height=40):
        self.width = width
        self.height = height
        self.charset = string.digits + string.ascii_uppercase
    
    def generate_simple_captcha(self, text=None, save_path=None):
        """生成简单的验证码图像"""
        if text is None:
            text = ''.join(random.choices(self.charset, k=4))
        
        # 创建图像
        image = np.ones((self.height, self.width, 3), dtype=np.uint8) * 255
        
        # 随机字体和大小
        fonts = [
            cv2.FONT_HERSHEY_SIMPLEX,
            cv2.FONT_HERSHEY_PLAIN,
            cv2.FONT_HERSHEY_DUPLEX,
            cv2.FONT_HERSHEY_COMPLEX
        ]
        
        font = random.choice(fonts)
        font_scale = random.uniform(0.8, 1.2)
        thickness = random.randint(1, 2)
        
        # 计算字符间距
        char_width = self.width // len(text)
        
        for i, char in enumerate(text):
            # 随机颜色（深色）
            color = (
                random.randint(0, 100),
                random.randint(0, 100),
                random.randint(0, 100)
            )
            
            # 计算位置
            x = i * char_width + random.randint(5, 15)
            y = random.randint(25, 35)
            
            # 绘制字符
            cv2.putText(image, char, (x, y), font, font_scale, color, thickness)
        
        # 添加噪声线条
        for _ in range(random.randint(2, 5)):
            start_point = (random.randint(0, self.width), random.randint(0, self.height))
            end_point = (random.randint(0, self.width), random.randint(0, self.height))
            color = (random.randint(100, 200), random.randint(100, 200), random.randint(100, 200))
            cv2.line(image, start_point, end_point, color, 1)
        
        # 添加噪声点
        for _ in range(random.randint(50, 100)):
            x = random.randint(0, self.width - 1)
            y = random.randint(0, self.height - 1)
            color = (random.randint(0, 255), random.randint(0, 255), random.randint(0, 255))
            image[y, x] = color
        
        if save_path:
            cv2.imwrite(save_path, image)
        
        return image, text
    
    def generate_batch_captchas(self, num_captchas, output_dir):
        """批量生成验证码"""
        os.makedirs(output_dir, exist_ok=True)
        
        generated_data = []
        
        for i in range(num_captchas):
            image, text = self.generate_simple_captcha()
            filename = f"{text}_{i:04d}.png"
            filepath = os.path.join(output_dir, filename)
            
            cv2.imwrite(filepath, image)
            generated_data.append((filepath, text))
            
            if (i + 1) % 100 == 0:
                print(f"已生成 {i + 1} 个验证码")
        
        print(f"总共生成了 {num_captchas} 个验证码，保存在 {output_dir}")
        return generated_data


def create_test_images():
    """创建一些测试图像"""
    generator = CaptchaGenerator()
    
    # 创建测试目录
    os.makedirs('test_images', exist_ok=True)
    
    # 生成一些测试验证码
    test_texts = ['1234', 'ABCD', '5678', 'WXYZ', '9ABC', 'DEF0']
    
    for text in test_texts:
        image, _ = generator.generate_simple_captcha(text)
        filename = f'test_images/{text}.png'
        cv2.imwrite(filename, image)
        print(f"创建测试图像: {filename}")
    
    # 生成更多随机验证码
    generator.generate_batch_captchas(20, 'test_images/random')


def visualize_preprocessing_steps(image_path):
    """可视化预处理步骤"""
    from image_processor import ImageProcessor
    import matplotlib.pyplot as plt
    
    processor = ImageProcessor()
    
    # 加载图像
    image = processor.load_image(image_path)
    if image is None:
        print("无法加载图像")
        return
    
    # 预处理步骤
    gray = processor.to_grayscale(image)
    denoised = processor.denoise(gray)
    binary = processor.binarize(denoised)
    cleaned = processor.remove_noise_morphology(binary)
    characters, boxes = processor.segment_characters(image, cleaned)
    
    # 显示结果
    fig, axes = plt.subplots(2, 3, figsize=(15, 10))
    
    axes[0, 0].imshow(image)
    axes[0, 0].set_title('原始图像')
    axes[0, 0].axis('off')
    
    axes[0, 1].imshow(gray, cmap='gray')
    axes[0, 1].set_title('灰度图像')
    axes[0, 1].axis('off')
    
    axes[0, 2].imshow(denoised, cmap='gray')
    axes[0, 2].set_title('降噪后')
    axes[0, 2].axis('off')
    
    axes[1, 0].imshow(binary, cmap='gray')
    axes[1, 0].set_title('二值化')
    axes[1, 0].axis('off')
    
    axes[1, 1].imshow(cleaned, cmap='gray')
    axes[1, 1].set_title('形态学处理')
    axes[1, 1].axis('off')
    
    # 显示分割的字符
    if characters:
        # 将字符水平拼接
        if len(characters) > 1:
            max_height = max(char.shape[0] for char in characters)
            padded_chars = []
            for char in characters:
                if char.shape[0] < max_height:
                    padding = max_height - char.shape[0]
                    char = np.pad(char, ((0, padding), (0, 0)), mode='constant', constant_values=255)
                padded_chars.append(char)
            combined_chars = np.hstack(padded_chars)
        else:
            combined_chars = characters[0]
        
        axes[1, 2].imshow(combined_chars, cmap='gray')
        axes[1, 2].set_title(f'分割字符 ({len(characters)}个)')
    else:
        axes[1, 2].text(0.5, 0.5, '未检测到字符', ha='center', va='center')
        axes[1, 2].set_title('分割字符')
    axes[1, 2].axis('off')
    
    plt.tight_layout()
    plt.show()


def benchmark_models():
    """比较不同模型的性能"""
    from captcha_recognizer import CaptchaRecognizer
    import time
    
    # 创建测试数据
    create_test_images()
    
    models = ['svm', 'random_forest']
    results = {}
    
    for model_type in models:
        print(f"\n测试 {model_type} 模型...")
        
        recognizer = CaptchaRecognizer(f'models/{model_type}_model.pkl')
        
        # 训练模型
        start_time = time.time()
        success = recognizer.train_model(model_type=model_type, num_samples_per_char=50)
        training_time = time.time() - start_time
        
        if not success:
            print(f"{model_type} 模型训练失败")
            continue
        
        # 测试识别
        test_images = [
            'test_images/1234.png',
            'test_images/ABCD.png',
            'test_images/5678.png'
        ]
        
        correct = 0
        total = 0
        recognition_times = []
        
        for image_path in test_images:
            if os.path.exists(image_path):
                true_label = os.path.basename(image_path).split('.')[0]
                
                start_time = time.time()
                result = recognizer.recognize(image_path)
                recognition_time = time.time() - start_time
                
                recognition_times.append(recognition_time)
                
                if result and result['result'] == true_label:
                    correct += 1
                total += 1
                
                print(f"  {image_path}: 真实={true_label}, 预测={result['result'] if result else 'None'}")
        
        accuracy = correct / total if total > 0 else 0
        avg_recognition_time = np.mean(recognition_times) if recognition_times else 0
        
        results[model_type] = {
            'accuracy': accuracy,
            'training_time': training_time,
            'avg_recognition_time': avg_recognition_time
        }
        
        print(f"  准确率: {accuracy:.4f}")
        print(f"  训练时间: {training_time:.2f}秒")
        print(f"  平均识别时间: {avg_recognition_time:.4f}秒")
    
    # 显示比较结果
    print("\n=== 模型性能比较 ===")
    for model_type, metrics in results.items():
        print(f"{model_type}:")
        print(f"  准确率: {metrics['accuracy']:.4f}")
        print(f"  训练时间: {metrics['training_time']:.2f}秒")
        print(f"  平均识别时间: {metrics['avg_recognition_time']:.4f}秒")


if __name__ == "__main__":
    # 创建测试图像
    create_test_images()
    
    # 可视化预处理步骤
    if os.path.exists('test_images/1234.png'):
        visualize_preprocessing_steps('test_images/1234.png')
    
    # 性能基准测试
    # benchmark_models()
