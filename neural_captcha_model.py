#!/usr/bin/env python3
"""
基于神经网络的验证码识别模型（使用MLPClassifier模拟CNN效果）
"""

import os
import numpy as np
import cv2
import string
import random
from collections import Counter
import pickle
import glob

from sklearn.neural_network import MLPClassifier
from sklearn.model_selection import train_test_split
from sklearn.preprocessing import LabelEncoder, StandardScaler
from sklearn.metrics import classification_report, accuracy_score

from improved_image_processor import ImprovedImageProcessor


class NeuralCaptchaModel:
    """基于神经网络的验证码识别模型"""
    
    def __init__(self, img_height=28, img_width=28):
        self.img_height = img_height
        self.img_width = img_width
        self.charset = string.digits + string.ascii_lowercase
        self.label_encoder = LabelEncoder()
        self.label_encoder.fit(list(self.charset))
        self.scaler = StandardScaler()
        self.model = None
        self.image_processor = ImprovedImageProcessor()
        
    def create_neural_model(self):
        """创建神经网络模型"""
        # 使用MLPClassifier模拟CNN的多层结构
        model = MLPClassifier(
            hidden_layer_sizes=(512, 256, 128, 64),  # 4层隐藏层，模拟CNN的深度
            activation='relu',
            solver='adam',
            alpha=0.001,  # L2正则化
            batch_size=64,
            learning_rate='adaptive',
            learning_rate_init=0.001,
            max_iter=500,
            early_stopping=True,
            validation_fraction=0.1,
            n_iter_no_change=20,
            random_state=42,
            verbose=True
        )
        return model
    
    def preprocess_char_image(self, char_image):
        """预处理单个字符图像"""
        # 确保是灰度图
        if len(char_image.shape) == 3:
            char_image = cv2.cvtColor(char_image, cv2.COLOR_BGR2GRAY)
        
        # 调整大小
        char_image = cv2.resize(char_image, (self.img_width, self.img_height))
        
        # 归一化到0-1
        char_image = char_image.astype(np.float32) / 255.0
        
        # 展平为一维向量（MLPClassifier需要）
        char_image = char_image.flatten()
        
        return char_image
    
    def load_training_data(self, image_dir, max_samples_per_char=30):
        """加载训练数据"""
        print(f"从 {image_dir} 加载神经网络训练数据...")
        
        X = []
        y = []
        
        # 获取所有图片文件
        image_files = [f for f in os.listdir(image_dir) 
                      if f.lower().endswith(('.png', '.jpg', '.jpeg'))]
        
        print(f"找到 {len(image_files)} 个图片文件")
        
        # 随机打乱文件顺序
        random.shuffle(image_files)
        
        successful_samples = 0
        failed_samples = 0
        char_counts = {char: 0 for char in self.charset}
        
        for filename in image_files:
            # 从文件名提取标签
            label = os.path.splitext(filename)[0].lower()
            
            # 验证标签
            if len(label) != 4:
                failed_samples += 1
                continue
            
            if not all(c in self.charset for c in label):
                failed_samples += 1
                continue
            
            # 检查是否需要限制样本数量
            if max_samples_per_char:
                skip = False
                for char in label:
                    if char_counts[char] >= max_samples_per_char:
                        skip = True
                        break
                if skip:
                    continue
            
            image_path = os.path.join(image_dir, filename)
            
            try:
                # 使用改进的预处理分割字符
                characters, _ = self.image_processor.preprocess_image_improved(image_path)
                
                if not characters or len(characters) != 4:
                    failed_samples += 1
                    continue
                
                # 为每个字符准备数据
                for i, char_image in enumerate(characters):
                    char_label = label[i]
                    
                    # 预处理字符图像
                    processed_char = self.preprocess_char_image(char_image)
                    
                    # 编码标签
                    encoded_label = self.label_encoder.transform([char_label])[0]
                    
                    X.append(processed_char)
                    y.append(encoded_label)
                    char_counts[char_label] += 1
                
                successful_samples += 1
                
                if successful_samples % 50 == 0:
                    print(f"已处理 {successful_samples} 个成功样本...")
                    
            except Exception as e:
                print(f"处理 {filename} 时出错: {e}")
                failed_samples += 1
                continue
        
        print(f"\n神经网络数据加载完成:")
        print(f"  成功处理: {successful_samples} 个图片")
        print(f"  失败: {failed_samples} 个图片")
        print(f"  总字符样本: {len(X)} 个")
        
        # 显示字符分布
        print(f"\n字符分布:")
        for char in sorted(self.charset):
            if char_counts[char] > 0:
                print(f"  {char}: {char_counts[char]} 个样本")
        
        return np.array(X), np.array(y), char_counts
    
    def train_model(self, X, y, test_size=0.2):
        """训练神经网络模型"""
        print(f"\n开始训练神经网络模型...")
        print(f"数据形状: {X.shape}")
        print(f"标签数量: {len(y)}")
        
        # 分割数据
        X_train, X_test, y_train, y_test = train_test_split(
            X, y, test_size=test_size, random_state=42, stratify=None
        )
        
        print(f"训练集: {len(X_train)} 样本")
        print(f"测试集: {len(X_test)} 样本")
        
        # 标准化特征
        X_train_scaled = self.scaler.fit_transform(X_train)
        X_test_scaled = self.scaler.transform(X_test)
        
        # 创建并训练模型
        self.model = self.create_neural_model()
        
        print(f"\n开始训练...")
        self.model.fit(X_train_scaled, y_train)
        
        # 评估模型
        y_pred = self.model.predict(X_test_scaled)
        test_accuracy = accuracy_score(y_test, y_pred)
        
        print(f"\n训练完成！测试准确率: {test_accuracy:.4f}")
        
        # 详细分类报告
        print(f"\n分类报告:")
        print(classification_report(y_test, y_pred))
        
        return test_accuracy
    
    def save_model(self, model_path):
        """保存模型"""
        os.makedirs(os.path.dirname(model_path), exist_ok=True)
        
        model_data = {
            'model': self.model,
            'scaler': self.scaler,
            'label_encoder': self.label_encoder,
            'charset': self.charset,
            'img_height': self.img_height,
            'img_width': self.img_width
        }
        
        with open(model_path, 'wb') as f:
            pickle.dump(model_data, f)
        
        print(f"神经网络模型已保存到: {model_path}")
    
    def load_model(self, model_path):
        """加载模型"""
        try:
            with open(model_path, 'rb') as f:
                model_data = pickle.load(f)
            
            self.model = model_data['model']
            self.scaler = model_data['scaler']
            self.label_encoder = model_data['label_encoder']
            self.charset = model_data['charset']
            self.img_height = model_data['img_height']
            self.img_width = model_data['img_width']
            
            print(f"神经网络模型已从 {model_path} 加载")
            return True
        except Exception as e:
            print(f"加载神经网络模型失败: {e}")
            return False
    
    def predict_character(self, char_image):
        """预测单个字符"""
        if self.model is None:
            return None, 0.0
        
        # 预处理图像
        processed_char = self.preprocess_char_image(char_image)
        processed_char = self.scaler.transform([processed_char])
        
        # 预测
        prediction = self.model.predict(processed_char)[0]
        
        # 获取置信度
        if hasattr(self.model, 'predict_proba'):
            probabilities = self.model.predict_proba(processed_char)[0]
            confidence = np.max(probabilities)
        else:
            confidence = 1.0
        
        # 解码标签
        predicted_char = self.label_encoder.inverse_transform([prediction])[0]
        
        return predicted_char, confidence
    
    def predict_captcha(self, image_path):
        """预测整个验证码"""
        if self.model is None:
            return None
        
        # 分割字符
        characters, _ = self.image_processor.preprocess_image_improved(image_path)
        
        if not characters:
            return None
        
        # 预测每个字符
        result = ""
        confidences = []
        
        for char_image in characters:
            predicted_char, confidence = self.predict_character(char_image)
            if predicted_char:
                result += predicted_char
                confidences.append(confidence)
        
        avg_confidence = np.mean(confidences) if confidences else 0.0
        
        return {
            'result': result,
            'confidence': avg_confidence,
            'character_confidences': confidences,
            'character_count': len(characters)
        }


def main():
    """主函数"""
    print("=== 基于神经网络的验证码识别模型训练 ===")
    
    # 初始化模型
    neural_model = NeuralCaptchaModel()
    
    # 配置
    training_dir = 'test_images'
    test_image_path = 'guess_images/image.png'
    model_path = 'models/neural_captcha_model.pkl'
    
    # 1. 加载训练数据
    X, y, char_counts = neural_model.load_training_data(
        training_dir, 
        max_samples_per_char=30  # 每个字符最多30个样本
    )
    
    if len(X) == 0:
        print("没有可用的训练数据")
        return
    
    # 2. 训练模型
    accuracy = neural_model.train_model(X, y)
    
    # 3. 保存模型
    neural_model.save_model(model_path)
    
    # 4. 测试单个图片
    if os.path.exists(test_image_path):
        print(f"\n测试图片: {test_image_path}")
        result = neural_model.predict_captcha(test_image_path)
        
        if result:
            print(f"识别结果: {result['result']}")
            print(f"置信度: {result['confidence']:.3f}")
            for i, (char, conf) in enumerate(zip(result['result'], result['character_confidences'])):
                print(f"  字符{i+1}: {char} (置信度: {conf:.3f})")
        else:
            print("识别失败")
    
    print(f"\n✅ 神经网络模型训练完成！")
    print(f"模型文件: {model_path}")


if __name__ == "__main__":
    main()
