#!/usr/bin/env python3
"""
验证码识别工具简单演示
"""

import os
import sys
from captcha_recognizer import CaptchaRecognizer
from utils import CaptchaGenerator


def simple_demo():
    """简单演示"""
    print("=== 验证码识别工具演示 ===")
    
    # 创建必要的目录
    os.makedirs('models', exist_ok=True)
    os.makedirs('test_images', exist_ok=True)
    
    # 1. 创建识别器
    print("1. 初始化识别器...")
    recognizer = CaptchaRecognizer('models/demo_model.pkl')
    
    # 2. 训练模型（使用较少样本快速演示）
    print("2. 训练模型（这可能需要几分钟）...")
    success = recognizer.train_model(
        model_type='svm',
        num_samples_per_char=30  # 减少样本数量以加快训练
    )
    
    if not success:
        print("模型训练失败")
        return
    
    print("   模型训练完成！")
    
    # 3. 生成测试验证码
    print("3. 生成测试验证码...")
    generator = CaptchaGenerator()
    
    test_cases = [
        ('1234', 'test_images/demo_1234.png'),
        ('ABCD', 'test_images/demo_ABCD.png'),
        ('5678', 'test_images/demo_5678.png'),
        ('WXYZ', 'test_images/demo_WXYZ.png')
    ]
    
    for text, image_path in test_cases:
        generator.generate_simple_captcha(text, image_path)
        print(f"   生成验证码: {text} -> {image_path}")
    
    # 4. 识别验证码
    print("4. 识别验证码...")
    print("-" * 50)
    
    correct = 0
    total = len(test_cases)
    
    for text, image_path in test_cases:
        result = recognizer.recognize(image_path)
        
        if result:
            predicted = result['result']
            confidence = result['confidence']
            is_correct = predicted == text
            
            if is_correct:
                correct += 1
            
            print(f"图像: {os.path.basename(image_path)}")
            print(f"真实值: {text}")
            print(f"识别结果: {predicted}")
            print(f"置信度: {confidence:.3f}")
            print(f"正确性: {'✓' if is_correct else '✗'}")
            print("-" * 30)
        else:
            print(f"识别失败: {image_path}")
    
    # 5. 显示统计结果
    accuracy = correct / total if total > 0 else 0
    print(f"总体准确率: {accuracy:.3f} ({correct}/{total})")
    
    # 6. 演示命令行使用
    print("\n5. 命令行使用示例:")
    print("   训练模型:")
    print("   python captcha_recognizer.py --action train --model models/my_model.pkl")
    print("   识别验证码:")
    print("   python captcha_recognizer.py --action recognize --image test_images/demo_1234.png --model models/demo_model.pkl")
    
    print("\n演示完成！")


def quick_recognition_demo():
    """快速识别演示（使用已训练的模型）"""
    print("=== 快速识别演示 ===")
    
    model_path = 'models/demo_model.pkl'
    
    if not os.path.exists(model_path):
        print("没有找到预训练模型，请先运行完整演示")
        return
    
    # 加载模型
    recognizer = CaptchaRecognizer(model_path)
    
    if not recognizer.is_model_loaded:
        print("模型加载失败")
        return
    
    # 生成新的测试验证码
    generator = CaptchaGenerator()
    test_text = 'TEST'
    image_path = 'test_images/quick_test.png'
    
    generator.generate_simple_captcha(test_text, image_path)
    print(f"生成测试验证码: {test_text}")
    
    # 识别
    result = recognizer.recognize(image_path)
    
    if result:
        print(f"识别结果: {result['result']}")
        print(f"置信度: {result['confidence']:.3f}")
        print(f"字符数量: {result['character_count']}")
        print(f"正确性: {'✓' if result['result'] == test_text else '✗'}")
    else:
        print("识别失败")


def interactive_demo():
    """交互式演示"""
    print("=== 交互式验证码识别 ===")
    
    model_path = 'models/demo_model.pkl'
    
    if not os.path.exists(model_path):
        print("没有找到预训练模型，请先运行: python demo.py --full")
        return
    
    recognizer = CaptchaRecognizer(model_path)
    generator = CaptchaGenerator()
    
    if not recognizer.is_model_loaded:
        print("模型加载失败")
        return
    
    print("输入4位字符（数字和大写字母），输入'quit'退出")
    
    while True:
        user_input = input("\n请输入验证码内容（如1234或ABCD）: ").strip().upper()
        
        if user_input.lower() == 'quit':
            break
        
        if len(user_input) != 4:
            print("请输入4位字符")
            continue
        
        # 检查字符是否有效
        valid_chars = set('0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZ')
        if not all(c in valid_chars for c in user_input):
            print("只支持数字和大写字母")
            continue
        
        # 生成验证码
        image_path = f'test_images/interactive_{user_input}.png'
        generator.generate_simple_captcha(user_input, image_path)
        
        # 识别
        result = recognizer.recognize(image_path)
        
        if result:
            predicted = result['result']
            confidence = result['confidence']
            is_correct = predicted == user_input
            
            print(f"生成的验证码: {user_input}")
            print(f"识别结果: {predicted}")
            print(f"置信度: {confidence:.3f}")
            print(f"正确性: {'✓' if is_correct else '✗'}")
            
            if not is_correct:
                print(f"识别错误！期望: {user_input}, 实际: {predicted}")
        else:
            print("识别失败")
    
    print("退出交互式演示")


def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description='验证码识别工具演示')
    parser.add_argument('--full', action='store_true', help='运行完整演示（包括训练）')
    parser.add_argument('--quick', action='store_true', help='快速识别演示')
    parser.add_argument('--interactive', action='store_true', help='交互式演示')
    
    args = parser.parse_args()
    
    try:
        if args.full:
            simple_demo()
        elif args.quick:
            quick_recognition_demo()
        elif args.interactive:
            interactive_demo()
        else:
            print("验证码识别工具演示")
            print("选择演示模式:")
            print("  --full        完整演示（包括模型训练）")
            print("  --quick       快速识别演示")
            print("  --interactive 交互式演示")
            print("\n示例:")
            print("  python demo.py --full")
            print("  python demo.py --quick")
            print("  python demo.py --interactive")
            
    except KeyboardInterrupt:
        print("\n用户中断")
    except Exception as e:
        print(f"运行出错: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
