#!/usr/bin/env python3
"""
分析验证码图片，调整预处理参数
"""

import os
import cv2
import numpy as np
import matplotlib.pyplot as plt
from image_processor import ImageProcessor


def analyze_single_captcha(image_path, show_steps=True):
    """分析单个验证码图片"""
    print(f"分析图片: {image_path}")
    
    processor = ImageProcessor()
    
    # 加载图像
    image = processor.load_image(image_path)
    if image is None:
        print("无法加载图像")
        return None
    
    print(f"图像尺寸: {image.shape}")
    
    # 预处理步骤
    gray = processor.to_grayscale(image)
    denoised = processor.denoise(gray)
    
    # 尝试不同的二值化方法
    binary_otsu = processor.binarize(denoised, method='otsu')
    binary_adaptive = processor.binarize(denoised, method='adaptive')
    binary_fixed = processor.binarize(denoised, method='fixed')
    
    # 形态学处理
    cleaned_otsu = processor.remove_noise_morphology(binary_otsu)
    cleaned_adaptive = processor.remove_noise_morphology(binary_adaptive)
    
    # 尝试字符分割
    contours_otsu = processor.find_character_contours(cleaned_otsu)
    contours_adaptive = processor.find_character_contours(cleaned_adaptive)
    
    print(f"OTSU二值化检测到 {len(contours_otsu)} 个轮廓")
    print(f"自适应二值化检测到 {len(contours_adaptive)} 个轮廓")
    
    if show_steps:
        # 显示处理步骤
        fig, axes = plt.subplots(3, 3, figsize=(15, 12))
        
        axes[0, 0].imshow(image)
        axes[0, 0].set_title('原始图像')
        axes[0, 0].axis('off')
        
        axes[0, 1].imshow(gray, cmap='gray')
        axes[0, 1].set_title('灰度图像')
        axes[0, 1].axis('off')
        
        axes[0, 2].imshow(denoised, cmap='gray')
        axes[0, 2].set_title('降噪后')
        axes[0, 2].axis('off')
        
        axes[1, 0].imshow(binary_otsu, cmap='gray')
        axes[1, 0].set_title('OTSU二值化')
        axes[1, 0].axis('off')
        
        axes[1, 1].imshow(binary_adaptive, cmap='gray')
        axes[1, 1].set_title('自适应二值化')
        axes[1, 1].axis('off')
        
        axes[1, 2].imshow(binary_fixed, cmap='gray')
        axes[1, 2].set_title('固定阈值二值化')
        axes[1, 2].axis('off')
        
        axes[2, 0].imshow(cleaned_otsu, cmap='gray')
        axes[2, 0].set_title(f'OTSU+形态学 ({len(contours_otsu)}个轮廓)')
        axes[2, 0].axis('off')
        
        axes[2, 1].imshow(cleaned_adaptive, cmap='gray')
        axes[2, 1].set_title(f'自适应+形态学 ({len(contours_adaptive)}个轮廓)')
        axes[2, 1].axis('off')
        
        # 显示轮廓
        contour_image = image.copy()
        if len(contours_otsu) > 0:
            cv2.drawContours(contour_image, contours_otsu, -1, (0, 255, 0), 2)
        axes[2, 2].imshow(contour_image)
        axes[2, 2].set_title('检测到的轮廓')
        axes[2, 2].axis('off')
        
        plt.tight_layout()
        plt.savefig(f'analysis_{os.path.basename(image_path)}.png', dpi=150, bbox_inches='tight')
        plt.show()
    
    return {
        'image_shape': image.shape,
        'contours_otsu': len(contours_otsu),
        'contours_adaptive': len(contours_adaptive)
    }


def analyze_multiple_captchas(image_dir, max_samples=5):
    """分析多个验证码图片"""
    print(f"分析目录: {image_dir}")
    
    image_files = [f for f in os.listdir(image_dir) 
                  if f.lower().endswith(('.png', '.jpg', '.jpeg'))]
    
    print(f"找到 {len(image_files)} 个图片文件")
    
    results = []
    
    for i, filename in enumerate(image_files[:max_samples]):
        image_path = os.path.join(image_dir, filename)
        print(f"\n--- 分析 {i+1}/{min(max_samples, len(image_files))}: {filename} ---")
        
        result = analyze_single_captcha(image_path, show_steps=(i < 3))
        if result:
            result['filename'] = filename
            results.append(result)
    
    # 统计结果
    print(f"\n=== 分析结果统计 ===")
    otsu_counts = [r['contours_otsu'] for r in results]
    adaptive_counts = [r['contours_adaptive'] for r in results]
    
    print(f"OTSU二值化轮廓数量: {otsu_counts}")
    print(f"自适应二值化轮廓数量: {adaptive_counts}")
    print(f"OTSU平均轮廓数: {np.mean(otsu_counts):.1f}")
    print(f"自适应平均轮廓数: {np.mean(adaptive_counts):.1f}")
    
    return results


def test_improved_segmentation(image_path):
    """测试改进的字符分割方法"""
    print(f"测试改进分割: {image_path}")
    
    processor = ImageProcessor()
    
    # 加载图像
    image = processor.load_image(image_path)
    if image is None:
        return None
    
    gray = processor.to_grayscale(image)
    
    # 尝试不同的预处理组合
    methods = [
        ('OTSU', 'otsu'),
        ('自适应', 'adaptive'),
        ('固定阈值', 'fixed')
    ]
    
    best_result = None
    best_count = 0
    
    for method_name, method in methods:
        print(f"\n尝试 {method_name} 二值化...")
        
        # 二值化
        binary = processor.binarize(gray, method=method)
        
        # 形态学处理
        cleaned = processor.remove_noise_morphology(binary)
        
        # 字符分割
        characters, boxes = processor.segment_characters(image, cleaned)
        
        print(f"  分割出 {len(characters)} 个字符")
        
        if len(characters) == 4:  # 期望的字符数量
            print(f"  ✓ {method_name} 方法成功分割出4个字符！")
            best_result = (characters, boxes, method_name)
            best_count = 4
            break
        elif len(characters) > best_count:
            best_result = (characters, boxes, method_name)
            best_count = len(characters)
    
    if best_result:
        characters, boxes, method_name = best_result
        print(f"\n最佳结果: {method_name} 方法，分割出 {len(characters)} 个字符")
        
        # 显示分割结果
        fig, axes = plt.subplots(1, len(characters) + 1, figsize=(15, 3))
        
        # 显示原图
        axes[0].imshow(image)
        axes[0].set_title('原图')
        axes[0].axis('off')
        
        # 显示分割的字符
        for i, char in enumerate(characters):
            axes[i+1].imshow(char, cmap='gray')
            axes[i+1].set_title(f'字符 {i+1}')
            axes[i+1].axis('off')
        
        plt.tight_layout()
        plt.savefig(f'segmentation_{os.path.basename(image_path)}.png', dpi=150, bbox_inches='tight')
        plt.show()
        
        return characters
    else:
        print("所有方法都无法正确分割字符")
        return None


def main():
    """主函数"""
    # 分析训练图片
    print("=== 分析训练图片 ===")
    results = analyze_multiple_captchas('test_images', max_samples=3)
    
    # 分析测试图片
    print("\n=== 分析测试图片 ===")
    if os.path.exists('guess_images/image.png'):
        analyze_single_captcha('guess_images/image.png', show_steps=True)
        
        print("\n=== 测试改进分割 ===")
        test_improved_segmentation('guess_images/image.png')
    
    # 给出建议
    print("\n=== 建议 ===")
    print("1. 如果大部分图片只能分割出1个字符，可能是字符连接在一起")
    print("2. 可以尝试调整形态学操作参数")
    print("3. 可以尝试不同的二值化阈值")
    print("4. 可能需要使用更复杂的字符分割算法")


if __name__ == "__main__":
    main()
