#!/usr/bin/env python3
"""
dddoc工具 - 验证码识别系统性能基准测试
"""

import os
import time
import statistics
import glob
from neural_captcha_model import NeuralCaptchaModel


class CaptchaBenchmark:
    """验证码识别性能基准测试类"""
    
    def __init__(self):
        self.neural_model = NeuralCaptchaModel()
        self.model_path = 'models/neural_captcha_model.pkl'
        self.results = []
        
    def load_model(self):
        """加载模型"""
        if not os.path.exists(self.model_path):
            print(f"❌ 模型文件不存在: {self.model_path}")
            return False
            
        return self.neural_model.load_model(self.model_path)
    
    def benchmark_single_image(self, image_path, true_label=None):
        """单张图片性能测试"""
        if not os.path.exists(image_path):
            return None
            
        # 多次测试取平均值
        times = []
        results = []
        
        for _ in range(3):  # 测试3次
            start_time = time.time()
            result = self.neural_model.predict_captcha(image_path)
            end_time = time.time()
            
            if result:
                times.append(end_time - start_time)
                results.append(result)
        
        if not times:
            return None
            
        avg_time = statistics.mean(times)
        final_result = results[0]  # 使用第一次的结果
        
        # 计算准确性
        is_correct = False
        if true_label and final_result:
            predicted = final_result['result'].lower()
            is_correct = predicted == true_label.lower()
        
        return {
            'image_path': image_path,
            'true_label': true_label,
            'predicted': final_result['result'] if final_result else None,
            'confidence': final_result['confidence'] if final_result else 0,
            'processing_time': avg_time,
            'is_correct': is_correct,
            'character_count': final_result['character_count'] if final_result else 0
        }
    
    def run_benchmark(self, test_dir='guess_images', max_images=None):
        """运行基准测试"""
        print("🚀 开始性能基准测试")
        print("=" * 60)
        
        # 获取测试图片
        image_files = glob.glob(os.path.join(test_dir, '*.png'))
        if max_images:
            image_files = image_files[:max_images]
        
        print(f"📁 测试目录: {test_dir}")
        print(f"🖼️  测试图片数量: {len(image_files)}")
        
        if not image_files:
            print("❌ 没有找到测试图片")
            return
        
        # 加载模型
        if not self.load_model():
            print("❌ 模型加载失败")
            return
        
        print("✅ 模型加载成功，开始测试...")
        print("-" * 60)
        
        # 测试每张图片
        for i, image_path in enumerate(image_files, 1):
            filename = os.path.basename(image_path)
            
            # 从文件名提取真实标签
            if filename == 'image.png':
                true_label = 'image'
            else:
                true_label = os.path.splitext(filename)[0].lower()
            
            print(f"[{i:2d}/{len(image_files)}] {filename}", end=" ... ")
            
            result = self.benchmark_single_image(image_path, true_label)
            
            if result:
                status = "✅" if result['is_correct'] else "❌"
                print(f"{status} {result['processing_time']:.3f}s (置信度: {result['confidence']:.3f})")
                self.results.append(result)
            else:
                print("❌ 处理失败")
        
        # 生成报告
        self.generate_report()
    
    def generate_report(self):
        """生成性能报告"""
        if not self.results:
            print("❌ 没有测试结果")
            return
        
        print("\n" + "=" * 60)
        print("📊 性能基准测试报告")
        print("=" * 60)
        
        # 基本统计
        total_images = len(self.results)
        correct_images = sum(1 for r in self.results if r['is_correct'])
        accuracy = (correct_images / total_images) * 100
        
        # 时间统计
        times = [r['processing_time'] for r in self.results]
        avg_time = statistics.mean(times)
        min_time = min(times)
        max_time = max(times)
        median_time = statistics.median(times)
        
        # 置信度统计
        confidences = [r['confidence'] for r in self.results]
        avg_confidence = statistics.mean(confidences)
        
        print(f"🎯 准确性指标:")
        print(f"   总图片数: {total_images}")
        print(f"   识别正确: {correct_images}")
        print(f"   识别错误: {total_images - correct_images}")
        print(f"   准确率: {accuracy:.2f}%")
        print(f"   平均置信度: {avg_confidence:.3f}")
        
        print(f"\n⏱️  性能指标:")
        print(f"   平均处理时间: {avg_time:.3f}秒")
        print(f"   最快处理时间: {min_time:.3f}秒")
        print(f"   最慢处理时间: {max_time:.3f}秒")
        print(f"   中位数时间: {median_time:.3f}秒")
        print(f"   处理速度: {1/avg_time:.1f} 图片/秒")
        
        # 性能等级评估
        print(f"\n🏆 性能等级评估:")
        
        # 准确率评级
        if accuracy >= 80:
            accuracy_grade = "A+ (优秀)"
        elif accuracy >= 70:
            accuracy_grade = "A (良好)"
        elif accuracy >= 60:
            accuracy_grade = "B (中等)"
        elif accuracy >= 50:
            accuracy_grade = "C (及格)"
        else:
            accuracy_grade = "D (需改进)"
        
        # 速度评级
        if avg_time <= 0.02:
            speed_grade = "A+ (极快)"
        elif avg_time <= 0.05:
            speed_grade = "A (很快)"
        elif avg_time <= 0.1:
            speed_grade = "B (快)"
        elif avg_time <= 0.2:
            speed_grade = "C (中等)"
        else:
            speed_grade = "D (较慢)"
        
        print(f"   准确率等级: {accuracy_grade}")
        print(f"   速度等级: {speed_grade}")
        
        # 错误分析
        errors = [r for r in self.results if not r['is_correct']]
        if errors:
            print(f"\n❌ 错误分析 (共{len(errors)}个错误):")
            for error in errors[:5]:  # 只显示前5个错误
                filename = os.path.basename(error['image_path'])
                print(f"   {filename}: {error['true_label']} -> {error['predicted']} "
                      f"(置信度: {error['confidence']:.3f})")
            
            if len(errors) > 5:
                print(f"   ... 还有 {len(errors) - 5} 个错误")
        
        # 系统信息
        print(f"\n🖥️  系统信息:")
        print(f"   模型文件: {self.model_path}")
        model_size = os.path.getsize(self.model_path) / (1024 * 1024)
        print(f"   模型大小: {model_size:.2f} MB")
        print(f"   字符集大小: {len(self.neural_model.charset)}")
        print(f"   图像尺寸: {self.neural_model.img_height}x{self.neural_model.img_width}")


def main():
    """主函数"""
    print("🔧 dddoc工具 - 验证码识别系统性能基准测试")
    print("=" * 60)
    
    benchmark = CaptchaBenchmark()
    
    # 运行基准测试
    benchmark.run_benchmark(test_dir='guess_images')
    
    print(f"\n🏁 基准测试完成！")


if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n\n⚠️  用户中断测试")
    except Exception as e:
        print(f"\n❌ 测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
