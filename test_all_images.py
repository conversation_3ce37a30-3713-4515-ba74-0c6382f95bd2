#!/usr/bin/env python3
"""
测试所有guess_images下的验证码图片
"""

import os
import glob
from train_improved import ImprovedCaptchaTrainer


def test_all_captcha_images():
    """测试所有验证码图片"""
    print("=== 验证码识别效果测试 ===")
    
    # 初始化训练器
    trainer = ImprovedCaptchaTrainer()
    
    # 模型路径
    model_path = 'models/improved_captcha_model.pkl'
    
    if not os.path.exists(model_path):
        print(f"❌ 模型文件不存在: {model_path}")
        print("请先运行 python train_improved.py 训练模型")
        return
    
    # 获取所有测试图片
    test_dir = 'guess_images'
    image_files = glob.glob(os.path.join(test_dir, '*.png'))
    image_files.sort()
    
    if not image_files:
        print(f"❌ 在 {test_dir} 中没有找到测试图片")
        return
    
    print(f"📁 测试目录: {test_dir}")
    print(f"🖼️  测试图片数量: {len(image_files)}")
    print(f"🤖 使用模型: {model_path}")
    print()
    
    # 统计变量
    total_images = len(image_files)
    correct_predictions = 0
    total_characters = 0
    correct_characters = 0
    
    results = []
    
    print("开始测试...")
    print("-" * 80)
    
    for i, image_path in enumerate(image_files, 1):
        # 从文件名提取真实标签
        filename = os.path.basename(image_path)
        true_label = os.path.splitext(filename)[0].lower()
        
        print(f"[{i:2d}/{total_images}] 测试: {filename}")
        print(f"    真实标签: {true_label}")
        
        # 识别验证码
        result = trainer.test_single_image(image_path, model_path)
        
        if result:
            predicted_label = result['result'].lower()
            confidence = result['confidence']
            
            print(f"    识别结果: {predicted_label}")
            print(f"    置信度: {confidence:.3f}")
            
            # 计算准确率
            is_correct = (predicted_label == true_label)
            if is_correct:
                correct_predictions += 1
                print("    ✅ 完全正确")
            else:
                print("    ❌ 识别错误")
            
            # 计算字符级准确率
            char_correct = 0
            for j, (true_char, pred_char) in enumerate(zip(true_label, predicted_label)):
                if true_char == pred_char:
                    char_correct += 1
                else:
                    print(f"        字符{j+1}: {true_char} -> {pred_char} ❌")
            
            total_characters += len(true_label)
            correct_characters += char_correct
            
            # 保存结果
            results.append({
                'filename': filename,
                'true_label': true_label,
                'predicted_label': predicted_label,
                'confidence': confidence,
                'is_correct': is_correct,
                'char_accuracy': char_correct / len(true_label) if len(true_label) > 0 else 0
            })
            
        else:
            print("    ❌ 识别失败")
            results.append({
                'filename': filename,
                'true_label': true_label,
                'predicted_label': 'FAILED',
                'confidence': 0.0,
                'is_correct': False,
                'char_accuracy': 0.0
            })
        
        print()
    
    # 计算总体统计
    print("=" * 80)
    print("📊 测试结果统计")
    print("=" * 80)
    
    image_accuracy = correct_predictions / total_images if total_images > 0 else 0
    char_accuracy = correct_characters / total_characters if total_characters > 0 else 0
    
    print(f"🖼️  图片级准确率: {correct_predictions}/{total_images} = {image_accuracy:.2%}")
    print(f"🔤 字符级准确率: {correct_characters}/{total_characters} = {char_accuracy:.2%}")
    
    # 平均置信度
    valid_results = [r for r in results if r['predicted_label'] != 'FAILED']
    if valid_results:
        avg_confidence = sum(r['confidence'] for r in valid_results) / len(valid_results)
        print(f"📈 平均置信度: {avg_confidence:.3f}")
    
    print()
    
    # 详细结果表格
    print("📋 详细结果:")
    print("-" * 80)
    print(f"{'文件名':<15} {'真实':<6} {'识别':<6} {'置信度':<8} {'状态':<6}")
    print("-" * 80)
    
    for result in results:
        status = "✅" if result['is_correct'] else "❌"
        if result['predicted_label'] == 'FAILED':
            status = "💥"
        
        print(f"{result['filename']:<15} {result['true_label']:<6} {result['predicted_label']:<6} "
              f"{result['confidence']:<8.3f} {status:<6}")
    
    print("-" * 80)
    
    # 错误分析
    errors = [r for r in results if not r['is_correct']]
    if errors:
        print(f"\n❌ 错误分析 ({len(errors)} 个错误):")
        for error in errors:
            if error['predicted_label'] != 'FAILED':
                print(f"  {error['filename']}: {error['true_label']} -> {error['predicted_label']}")
                # 分析每个字符的错误
                for i, (true_char, pred_char) in enumerate(zip(error['true_label'], error['predicted_label'])):
                    if true_char != pred_char:
                        print(f"    位置{i+1}: '{true_char}' 被识别为 '{pred_char}'")
            else:
                print(f"  {error['filename']}: 识别失败")
    
    print(f"\n🎯 总结:")
    print(f"  - 测试了 {total_images} 张验证码图片")
    print(f"  - 图片级准确率: {image_accuracy:.2%}")
    print(f"  - 字符级准确率: {char_accuracy:.2%}")
    if valid_results:
        print(f"  - 平均置信度: {avg_confidence:.3f}")
    
    return results


if __name__ == "__main__":
    test_all_captcha_images()
