#!/usr/bin/env python3
"""
验证码识别工具测试脚本
"""

import os
import sys
import unittest
import numpy as np
import cv2
from captcha_recognizer import CaptchaRecognizer
from image_processor import ImageProcessor
from feature_extractor import FeatureExtractor
from model_trainer import ModelTrainer
from utils import CaptchaGenerator


class TestImageProcessor(unittest.TestCase):
    """测试图像处理模块"""
    
    def setUp(self):
        self.processor = ImageProcessor()
        self.test_image_path = 'test_images/test_processor.png'
        
        # 创建测试图像
        os.makedirs('test_images', exist_ok=True)
        test_image = np.ones((50, 200, 3), dtype=np.uint8) * 255
        cv2.putText(test_image, 'TEST', (10, 35), cv2.FONT_HERSHEY_SIMPLEX, 1, (0, 0, 0), 2)
        cv2.imwrite(self.test_image_path, test_image)
    
    def test_load_image(self):
        """测试图像加载"""
        image = self.processor.load_image(self.test_image_path)
        self.assertIsNotNone(image)
        self.assertEqual(len(image.shape), 3)  # RGB图像
    
    def test_to_grayscale(self):
        """测试灰度转换"""
        image = self.processor.load_image(self.test_image_path)
        gray = self.processor.to_grayscale(image)
        self.assertEqual(len(gray.shape), 2)  # 灰度图像
    
    def test_binarize(self):
        """测试二值化"""
        image = self.processor.load_image(self.test_image_path)
        gray = self.processor.to_grayscale(image)
        binary = self.processor.binarize(gray)
        
        # 检查二值化结果只包含0和255
        unique_values = np.unique(binary)
        self.assertTrue(all(val in [0, 255] for val in unique_values))
    
    def test_preprocess_image(self):
        """测试完整预处理流程"""
        characters, boxes = self.processor.preprocess_image(self.test_image_path)
        
        # 应该能检测到字符
        self.assertIsNotNone(characters)
        self.assertGreater(len(characters), 0)
        
        # 每个字符应该是28x28
        for char in characters:
            self.assertEqual(char.shape, (28, 28))


class TestFeatureExtractor(unittest.TestCase):
    """测试特征提取模块"""
    
    def setUp(self):
        self.extractor = FeatureExtractor()
        # 创建测试字符图像
        self.test_char = np.ones((28, 28), dtype=np.uint8) * 255
        cv2.putText(self.test_char, 'A', (5, 20), cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 0, 0), 2)
    
    def test_extract_pixel_features(self):
        """测试像素特征提取"""
        features = self.extractor.extract_pixel_features(self.test_char)
        self.assertEqual(features.shape, (784,))  # 28*28
        self.assertTrue(np.all(features >= 0) and np.all(features <= 1))  # 归一化
    
    def test_extract_hog_features(self):
        """测试HOG特征提取"""
        features = self.extractor.extract_hog_features(self.test_char)
        self.assertGreater(len(features), 0)
        self.assertIsInstance(features, np.ndarray)
    
    def test_extract_contour_features(self):
        """测试轮廓特征提取"""
        features = self.extractor.extract_contour_features(self.test_char)
        self.assertEqual(len(features), 7)  # 7个轮廓特征
    
    def test_extract_combined_features(self):
        """测试组合特征提取"""
        features = self.extractor.extract_combined_features(
            self.test_char, feature_types=['hog', 'contour']
        )
        self.assertGreater(len(features), 7)  # HOG + 轮廓特征


class TestModelTrainer(unittest.TestCase):
    """测试模型训练模块"""
    
    def setUp(self):
        self.trainer = ModelTrainer()
    
    def test_generate_training_data(self):
        """测试训练数据生成"""
        X, y = self.trainer.generate_training_data(num_samples_per_char=5)
        
        self.assertGreater(len(X), 0)
        self.assertEqual(len(X), len(y))
        self.assertGreater(X.shape[1], 0)  # 特征维度
    
    def test_model_training(self):
        """测试模型训练"""
        # 生成少量训练数据
        X, y = self.trainer.generate_training_data(num_samples_per_char=10)
        
        if len(X) > 0:
            # 测试SVM训练
            accuracy = self.trainer.train_svm_model(X, y)
            self.assertIsNotNone(self.trainer.model)
            self.assertGreaterEqual(accuracy, 0)
            self.assertLessEqual(accuracy, 1)


class TestCaptchaRecognizer(unittest.TestCase):
    """测试验证码识别器"""
    
    def setUp(self):
        self.recognizer = CaptchaRecognizer('models/test_model.pkl')
        self.generator = CaptchaGenerator()
    
    def test_train_and_recognize(self):
        """测试训练和识别流程"""
        # 训练模型
        success = self.recognizer.train_model(
            model_type='svm',
            num_samples_per_char=20
        )
        
        if success:
            # 生成测试验证码
            test_text = '1234'
            test_image_path = 'test_images/test_recognize.png'
            os.makedirs('test_images', exist_ok=True)
            
            self.generator.generate_simple_captcha(test_text, test_image_path)
            
            # 识别
            result = self.recognizer.recognize(test_image_path)
            
            self.assertIsNotNone(result)
            self.assertIn('result', result)
            self.assertIn('confidence', result)
            self.assertEqual(len(result['result']), 4)


class TestCaptchaGenerator(unittest.TestCase):
    """测试验证码生成器"""
    
    def setUp(self):
        self.generator = CaptchaGenerator()
    
    def test_generate_simple_captcha(self):
        """测试简单验证码生成"""
        image, text = self.generator.generate_simple_captcha('TEST')
        
        self.assertEqual(text, 'TEST')
        self.assertEqual(image.shape[:2], (40, 120))  # 高度x宽度
        self.assertEqual(len(image.shape), 3)  # RGB图像


def run_quick_test():
    """运行快速测试"""
    print("运行快速功能测试...")
    
    try:
        # 测试图像处理
        print("1. 测试图像处理...")
        processor = ImageProcessor()
        
        # 创建测试图像
        test_image = np.ones((50, 200, 3), dtype=np.uint8) * 255
        cv2.putText(test_image, 'TEST', (10, 35), cv2.FONT_HERSHEY_SIMPLEX, 1, (0, 0, 0), 2)
        
        os.makedirs('test_images', exist_ok=True)
        cv2.imwrite('test_images/quick_test.png', test_image)
        
        characters, boxes = processor.preprocess_image('test_images/quick_test.png')
        print(f"   检测到 {len(characters) if characters else 0} 个字符")
        
        # 测试特征提取
        print("2. 测试特征提取...")
        extractor = FeatureExtractor()
        
        if characters:
            features = extractor.extract_combined_features(characters[0])
            print(f"   特征维度: {len(features)}")
        
        # 测试验证码生成
        print("3. 测试验证码生成...")
        generator = CaptchaGenerator()
        image, text = generator.generate_simple_captcha('DEMO')
        print(f"   生成验证码: {text}")
        
        # 测试模型训练（小规模）
        print("4. 测试模型训练...")
        trainer = ModelTrainer()
        X, y = trainer.generate_training_data(num_samples_per_char=5)
        print(f"   生成训练数据: {len(X)} 个样本")
        
        if len(X) > 0:
            accuracy = trainer.train_svm_model(X, y)
            print(f"   模型准确率: {accuracy:.3f}")
        
        print("✓ 所有快速测试通过！")
        
    except Exception as e:
        print(f"✗ 测试失败: {e}")
        import traceback
        traceback.print_exc()


def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description='验证码识别工具测试')
    parser.add_argument('--quick', action='store_true', help='运行快速测试')
    parser.add_argument('--full', action='store_true', help='运行完整单元测试')
    
    args = parser.parse_args()
    
    if args.quick:
        run_quick_test()
    elif args.full:
        # 运行完整单元测试
        unittest.main(argv=[''], exit=False, verbosity=2)
    else:
        print("请选择测试类型:")
        print("  --quick  运行快速功能测试")
        print("  --full   运行完整单元测试")


if __name__ == "__main__":
    main()
